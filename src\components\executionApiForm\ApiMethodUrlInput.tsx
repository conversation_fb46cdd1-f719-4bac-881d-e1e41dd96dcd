import React from 'react';
import { Controller, useWatch, useFormContext } from 'react-hook-form';
import { Group } from '@mantine/core';
import { KanbanSelect } from 'kanban-design-system';
import { MethodTypeEnum, METHOD_COLORS } from '@common/constants/ExecutionConstants';
import classes from './ApiConfigSession.module.css';
import ParamAwareInput from './apiTabs/ParamAwareInput';
import { EXECUTION_API_URL_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface ApiMethodUrlInputProps {
  isViewMode: boolean;
  executionParams?: string[];
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

const MethodOptions = Object.values(MethodTypeEnum).map((m) => ({
  label: m,
  value: m,
}));

export const ApiMethodUrlInput: React.FC<ApiMethodUrlInputProps> = ({ isViewMode, registerEditor }) => {
  // Watch current method to apply color
  const form = useFormContext();
  const { control } = form;
  const currentMethod = useWatch({ control, name: 'apiInfo.method' });

  return (
    <Group className={classes.methodUrlRow}>
      <Controller
        name={'apiInfo.method'}
        control={control}
        render={({ field: { onChange, value = MethodTypeEnum.GET } }) => (
          <KanbanSelect
            data={MethodOptions}
            value={value}
            onChange={(val) => onChange(val)}
            className={classes.methodSelect}
            disabled={isViewMode}
            styles={{ input: { color: METHOD_COLORS[(currentMethod || MethodTypeEnum.GET) as MethodTypeEnum] } }}
          />
        )}
      />
      <Controller
        name={'apiInfo.url'}
        control={control}
        render={({ field }) => (
          <ParamAwareInput
            ref={field.ref}
            field={{
              name: field.name,
              value: field.value,
              onChange: field.onChange,
            }}
            disabled={isViewMode}
            registerEditor={registerEditor}
            isUrl
            maxLength={EXECUTION_API_URL_MAX_LENGTH}
          />
        )}
      />
    </Group>
  );
};
