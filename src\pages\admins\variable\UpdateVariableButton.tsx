import React, { useCallback, useEffect } from 'react';
import { KanbanButton, KanbanIconButton, KanbanTooltip } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from 'react-hook-form';
import useFetch from '@core/hooks/useFetch';
import { VariableApi } from '@api/VariableApi';
import { Variable } from '@core/schema/Variable';
import { IconEdit, IconEye } from '@tabler/icons-react';
import Modal from '@components/Modal';
import { Box } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';
import { isAnyPermissions } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@models/AclPermission';
import { VariableModel, VariableModelSchema } from '@models/VariableModel';
import VariableFormContainer from './VariableFormContainer';
import { DataTypeEnum, ExecutionTypeEnum, VariableTypeEnum } from '@common/constants/ExecutionConstants';
import { ExecutionApi } from '@api/ExecutionApi';

const DEFAULT_VARIABLE_VALUE: VariableModel = {
  name: '',
  description: '',
  value: '',
  type: VariableTypeEnum.FIXED_VALUE,
  enableExpiration: false,
  variableDetails: [],
  executionId: '',
  dataType: undefined,
  expirationTime: undefined,
  hidden: false,
};

interface Props {
  variable: Variable;
  onUpdateSuccess: () => void;
}

const UpdateVariableButton = ({ onUpdateSuccess, variable }: Props) => {
  const [opened, { close, open }] = useDisclosure();
  const hasEditPermission = isAnyPermissions([AclPermission.variableEdit]);
  const form = useForm<VariableModel>({
    defaultValues: DEFAULT_VARIABLE_VALUE,
    resolver: zodResolver(VariableModelSchema),
    mode: 'onChange', // Enable validation on change
  });
  const { getValues, reset } = form;

  // Flag to prevent useEffect from resetting when user is changing type
  const isInitialLoad = React.useRef(true);
  const { data: variableArrayData } = useFetch(VariableApi.findById(variable.id), { enabled: opened, placeholderData: (prev) => prev });

  // Fetch execution data when modal is opened
  const { data: executionsData } = useFetch(ExecutionApi.findByTypeNot(ExecutionTypeEnum.SQL), {
    enabled: opened,
    placeholderData: (prev) => prev,
  });
  const onClose = useCallback(() => {
    close();
    reset();
    // Reset flag for next time modal opens
    isInitialLoad.current = true;
  }, [close, reset]);
  const { mutate } = useMutate(VariableApi.createOrUpdate, {
    successNotification: 'Update Execution Variable successfully.',
    onSuccess: () => {
      onUpdateSuccess();
      onClose();
    },
  });
  useEffect(() => {
    // Only reset on initial load, not on subsequent changes
    if (!isInitialLoad.current) {
      return;
    }

    // Only proceed when both variable data and execution data are available
    if (variableArrayData?.data && variableArrayData.data.length > 0 && executionsData?.data) {
      const variables = variableArrayData.data;
      const firstVariable = variables[0];

      // Check if this is Dynamic Value with JSON data type (multiple variables)
      const isDynamicJson = firstVariable.type === VariableTypeEnum.DYNAMIC_VALUE && firstVariable.dataType === DataTypeEnum.JSON;

      if (isDynamicJson) {
        // For Dynamic JSON: map multiple variables to variableDetails
        const variableDetails = variables.map((variable) => ({
          id: variable.id,
          name: variable.name,
          jsonPath: variable.jsonPath || '',
          hidden: variable.hidden,
        }));

        reset({
          id: firstVariable.id,
          name: firstVariable.name,
          description: firstVariable.description || '',
          type: firstVariable.type,
          executionId: firstVariable.executionId,
          dataType: firstVariable.dataType,
          enableExpiration: firstVariable.enableExpiration || false,
          expirationTime: firstVariable.expirationTime,
          hidden: firstVariable.hidden,
          variableDetails: variableDetails,
          value: firstVariable.value || '',
        });
        // Mark as no longer initial load
        isInitialLoad.current = false;
        // Note: Removed trigger() to avoid circular dependency with useEffect
      } else {
        // For Fixed Value or Dynamic RAW: single variable
        const variableData = firstVariable;
        reset({
          id: variableData.id,
          name: variableData.name, // For FIXED_VALUE and DYNAMIC_VALUE + RAW
          description: variableData.description || '',
          type: variableData.type,
          executionId: variableData.executionId,
          dataType: variableData.dataType,
          enableExpiration: variableData.enableExpiration || false,
          expirationTime: variableData.expirationTime,
          hidden: variableData.hidden,
          value: variableData?.hidden ? '' : variableData?.value,
          variableDetails: [], // RAW type doesn't use variableDetails, only JSON type does
        });
        // Mark as no longer initial load
        isInitialLoad.current = false;
        // Note: Removed trigger() to avoid circular dependency with useEffect
      }
    }
  }, [variableArrayData, executionsData, reset]);
  const onSaveClick = useCallback(() => {
    const formData = getValues();

    // Clean up variableDetails for FIXED_VALUE and DYNAMIC_VALUE + RAW
    if (
      formData.type === VariableTypeEnum.FIXED_VALUE ||
      (formData.type === VariableTypeEnum.DYNAMIC_VALUE && formData.dataType === DataTypeEnum.RAW)
    ) {
      formData.variableDetails = [];
    }

    mutate(formData);
  }, [getValues, mutate]);
  const { formState } = form;
  return (
    <>
      <KanbanTooltip label='Edit'>
        <KanbanIconButton variant='transparent' size={'sm'} onClick={open}>
          {hasEditPermission ? <IconEdit /> : <IconEye />}
        </KanbanIconButton>
      </KanbanTooltip>
      <Modal
        size='xl'
        opened={opened}
        onClose={onClose}
        title={hasEditPermission ? 'Update Variable' : 'View Variable'}
        actions={
          hasEditPermission ? (
            <KanbanButton onClick={onSaveClick} disabled={!formState.isValid}>
              Save
            </KanbanButton>
          ) : undefined
        }>
        <Box p='xs'>
          <VariableFormContainer form={form} readonly={!hasEditPermission} />

          {/* Debug Form Validation */}
          {!formState.isValid && (
            <Box mt='md' p='sm' style={{ backgroundColor: '#ffe6e6', border: '1px solid #ff4444', borderRadius: '4px' }}>
              <strong style={{ color: '#cc0000' }}>Form Validation Errors:</strong>
              <pre style={{ color: '#cc0000', fontSize: '12px', marginTop: '8px' }}>{JSON.stringify(formState.errors, null, 2)}</pre>

              <div style={{ marginTop: '12px' }}>
                <strong style={{ color: '#cc0000' }}>Current Form Values:</strong>
                <pre style={{ color: '#333', fontSize: '11px', marginTop: '4px', backgroundColor: '#f5f5f5', padding: '8px', borderRadius: '3px' }}>
                  {JSON.stringify(getValues(), null, 2)}
                </pre>
              </div>

              <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                <strong>Form State:</strong>
                <br />
                isValid: {formState.isValid.toString()}
                <br />
                isDirty: {formState.isDirty.toString()}
                <br />
                isSubmitted: {formState.isSubmitted.toString()}
                <br />
                <br />
                <strong>Data Loading Status:</strong>
                <br />
                Variable Data: {variableArrayData?.data ? 'Loaded' : 'Loading...'}
                <br />
                Execution Data: {executionsData?.data ? `Loaded (${(executionsData.data as any[])?.length || 0} items)` : 'Loading...'}
              </div>
            </Box>
          )}
        </Box>
      </Modal>
    </>
  );
};

export default UpdateVariableButton;
