import React, { useEffect } from 'react';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';

export const TestVariableAPI = () => {
  const { data: variableData, isLoading, error, refetch } = useFetch(VariableApi.findAll(), { showLoading: false });

  useEffect(() => {
    console.log('=== TEST VARIABLE API ===');
    console.log('isLoading:', isLoading);
    console.log('error:', error);
    console.log('variableData:', variableData);
    console.log('variableData?.data:', variableData?.data);
    if (variableData?.data) {
      console.log('Variables count:', variableData.data.length);
      variableData.data.forEach((v: any, index: number) => {
        console.log(`Variable ${index}:`, v);
      });
    }
  }, [variableData, isLoading, error]);

  return (
    <div style={{ padding: '20px', border: '1px solid red', margin: '10px' }}>
      <h3>Test Variable API</h3>
      <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
      <p>Error: {error ? JSON.stringify(error) : 'None'}</p>
      <p>Data: {variableData ? 'Loaded' : 'Not loaded'}</p>
      <p>Variables count: {variableData?.data?.length || 0}</p>
      <button onClick={() => refetch()}>Refetch</button>
      <div>
        <h4>Variables:</h4>
        {variableData?.data?.map((v: any, index: number) => (
          <div key={index} style={{ border: '1px solid gray', margin: '5px', padding: '5px' }}>
            <strong>{v.name}</strong>: {v.value} (hidden: {v.hidden ? 'Yes' : 'No'})
          </div>
        ))}
      </div>
    </div>
  );
};
