import React, { useEffect, useRef } from 'react';
import { Box } from '@mantine/core';
import { Controller, UseFieldArrayReturn, Control, UseFormGetValues, Path, UseFormSetValue } from 'react-hook-form';
import { KanbanCheckbox, KanbanTitle } from 'kanban-design-system';
import { IconTrash } from '@tabler/icons-react';
import classes from '../ApiConfigSession.module.css';
import { ExecutionModel } from '@models/ExecutionModel';
import { EXECUTION_API_DESCRIPTION_MAX_LENGTH, EXECUTION_API_KEY_MAX_LENGTH } from '@common/constants/ValidationConstant';
import ParamAwareInput from './ParamAwareInput';
import { DEFAULT_KEY_VALUE } from '../ExcecutionDefaultValues';

export interface KeyValueRow {
  id: string;
  key: string;
  value: string;
  description: string;
  enable: boolean;
  autoGenerated?: boolean;
}

interface RenderTableProps {
  fa: UseFieldArrayReturn<ExecutionModel, 'apiInfo.headers' | 'apiInfo.params' | 'apiInfo.body.formUrlEncoded'>;
  control: Control<ExecutionModel>;
  namePrefix: 'apiInfo.headers' | 'apiInfo.params' | 'apiInfo.body.formUrlEncoded';
  isViewMode: boolean;
  executionParams?: string[];
  getValues: UseFormGetValues<ExecutionModel>;
  setValue: UseFormSetValue<ExecutionModel>;
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
  setFocus: (name: Path<ExecutionModel>) => void;
}

const COLUMNS: (keyof KeyValueRow)[] = ['key', 'value', 'description'];

export const RenderTable: React.FC<RenderTableProps> = ({
  control,
  executionParams = [],
  fa,
  isViewMode,
  namePrefix,
  registerEditor,
  setFocus,
  setValue,
}) => {
  const shouldDisableRow = (field: KeyValueRow) => namePrefix === 'apiInfo.headers' && field.autoGenerated;

  const editorRefs = useRef<Record<number, Record<string, { focus: () => void }>>>({});
  const currentFocusRef = useRef<{ focus: () => void } | null>(null);

  const handleAutoAppend = (idx: number, col: string) => {
    if (idx === fa.fields.length - 1) {
      currentFocusRef.current = editorRefs.current[idx]?.[col] ?? null;
      fa.append(DEFAULT_KEY_VALUE);
    }
  };

  useEffect(() => {
    if (currentFocusRef.current) {
      const timer = setTimeout(() => {
        currentFocusRef.current?.focus();
        currentFocusRef.current = null;
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [fa.fields.length]);

  useEffect(() => {
    const hasValidRow = fa.fields.some((row) => row.key?.trim() === '' && row.value?.trim() === '' && row.description?.trim() === '');
    if (!hasValidRow) {
      fa.append(DEFAULT_KEY_VALUE);
    }
  }, [fa]);

  return (
    <Box className={classes.tableWrap}>
      {/* Header */}
      <Box className={classes.tableHeader}>
        <Box className={classes.tableHeaderCell}></Box>
        {COLUMNS.map((col) => (
          <Box key={col} className={classes.tableHeaderCell}>
            <KanbanTitle fw={600} size='sm'>
              {col.charAt(0).toUpperCase() + col.slice(1)}
            </KanbanTitle>
          </Box>
        ))}
        <Box className={classes.tableHeaderCell}></Box>
      </Box>

      {/* Rows */}
      {fa.fields.map((field: KeyValueRow, idx: number) => {
        const isAutoGenerated = shouldDisableRow(field);

        return (
          <Box key={field.id} className={classes.tableRow}>
            {/* Checkbox */}
            <Box className={classes.tableCell}>
              <Controller
                name={`${namePrefix}.${idx}.enable` as Path<ExecutionModel>}
                control={control}
                render={({ field: { name, onBlur, onChange, ref, value } }) => (
                  <Box className={classes.checkboxContainer}>
                    <KanbanCheckbox
                      name={name}
                      checked={isAutoGenerated ? true : !!value}
                      onChange={(e) => {
                        if (!isAutoGenerated) {
                          onChange(e.currentTarget.checked);
                        }
                      }}
                      onBlur={onBlur}
                      disabled={isAutoGenerated || isViewMode}
                      ref={ref}
                    />
                  </Box>
                )}
              />
            </Box>

            {/* Inputs */}
            {COLUMNS.map((col) => (
              <Box key={col} className={classes.tableCell}>
                <Controller
                  name={`${namePrefix}.${idx}.${col}` as Path<ExecutionModel>}
                  control={control}
                  render={({ field: inputField }) => (
                    <ParamAwareInput
                      ref={(instance) => {
                        if (instance) {
                          if (!editorRefs.current[idx]) {
                            editorRefs.current[idx] = {};
                          }
                          editorRefs.current[idx][col] = instance;
                        }
                      }}
                      field={{
                        ...inputField,
                        value: typeof inputField.value === 'string' ? inputField.value : `${inputField.value ?? ''}`,
                        onChange: (e: { target: { value: string } }) => {
                          inputField.onChange(e);
                          setValue(`${namePrefix}.${idx}.enable` as Path<ExecutionModel>, true);
                          handleAutoAppend(idx, col);
                          setFocus(`${namePrefix}.${idx}.${col}` as Path<ExecutionModel>);
                        },
                      }}
                      disabled={isViewMode || isAutoGenerated}
                      executionParams={executionParams}
                      registerEditor={registerEditor}
                      maxLength={col === 'key' || col === 'value' ? EXECUTION_API_KEY_MAX_LENGTH : EXECUTION_API_DESCRIPTION_MAX_LENGTH}
                    />
                  )}
                />
              </Box>
            ))}

            {/* Delete */}
            <Box className={classes.tableCell}>
              <Box className={classes.actionContainer}>
                {!isViewMode && fa.fields.length > 1 && !isAutoGenerated && idx !== fa.fields.length - 1 && (
                  <IconTrash size={16} className={classes.deleteIcon} onClick={() => fa.remove(idx)} style={{ cursor: 'pointer', color: 'black' }} />
                )}
              </Box>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};
