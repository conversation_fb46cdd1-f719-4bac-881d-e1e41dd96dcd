.bellIndicator {
  background-color: var(--mantine-color-red-6);
  border: 2px solid var(--mantine-color-gray-3);
  font-size: 10px;
  font-weight: 600;
  min-width: 28px;
  min-height: 20px;
}
.bellButton {
  background-color: white;
  border: 1px solid var(--mantine-color-gray-4);
  border-radius: '50%';
  transition: all 0.2s ease;
}
.bellButton:hover {
  background-color: var(--mantine-primary-color-0);
  border-color: var(--mantine-primary-color-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.bellButton:active {
  transform: scale(0.95);
}
.notificationDropdown {
  padding: 0;
  border: 1px solid white;
}
.notificationItem {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: var(--mantine-radius-default);

  p {
    margin-block-start: 0;
    margin-block-end: 0;
  }
}

.tabsContainer {
  position: relative;
  display: flex;
  background-color: white;
  border-radius: var(--mantine-radius-sm);
  padding: calc(var(--mantine-spacing-xs) / 3);
  border: 1px solid var(--mantine-color-gray-5);
  gap: calc(var(--mantine-spacing-xs) / 2);
}

.tabControl {
  position: relative;
  display: block;
  text-align: center;
  text-decoration: none;
  color: var(--mantine-color-gray-7);
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  border-radius: var(--mantine-radius-sm);
  transition: color 150ms ease;
  padding: calc(var(--mantine-spacing-xs) / 2) var(--mantine-spacing-sm);
  flex: 1;
}

.tabControl[data-active] {
  color: var(--mantine-color-white);
}

.indicator {
  color: var(--mantine-color-white);
  background-color: var(--mantine-color-primary-5);
  border-radius: var(--mantine-radius-sm);
  box-shadow: var(--mantine-shadow-xs);
}

.controlLabel {
  font-weight: 500;
  position: relative;
  z-index: 1;
  text-align: center;
  font-size: 13px;
}