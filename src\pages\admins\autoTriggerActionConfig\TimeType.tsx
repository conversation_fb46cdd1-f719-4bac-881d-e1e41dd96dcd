import React from 'react';
import { Divider, Grid, TextInput } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { KanbanTitle } from 'kanban-design-system';
import Cron from 'cron-job/index';
import { MAX_CRON_EXPRESSION_LENGTH } from '@common/constants/ValidationConstant';
import { AutoTriggerActionConfigModel } from '@models/AutoTriggerActionConfigModel';

interface Props {
  form: UseFormReturn<AutoTriggerActionConfigModel>;
  isViewMode: boolean;
}

const TimeType = ({ form, isViewMode }: Props) => {
  const { control, setValue } = form;
  const inputRef = React.useRef<HTMLInputElement>(null);

  return (
    <form>
      <Grid>
        <Grid.Col span={4}>
          <KanbanTitle order={6}>Cron Expression</KanbanTitle>
        </Grid.Col>

        <Grid.Col span={8}>
          <Controller
            name='cronExpression'
            control={control}
            render={({ field, fieldState }) => (
              <TextInput
                label='Interval'
                maxLength={MAX_CRON_EXPRESSION_LENGTH}
                disabled={isViewMode}
                ref={inputRef}
                value={field.value}
                onChange={(e) => {
                  field.onChange(e.target.value);
                }}
                error={fieldState.error?.message}
              />
            )}
          />
        </Grid.Col>
      </Grid>

      <Divider my='sm' label='OR' labelPosition='center' />
      <Controller
        name='cronExpression'
        control={control}
        render={({ field }) => (
          <Cron
            value={field.value || ''}
            disabled={isViewMode}
            setValue={(val: string) => {
              setValue('cronExpression', val);
              field.onChange(val);
            }}
          />
        )}
      />
    </form>
  );
};

export default TimeType;
