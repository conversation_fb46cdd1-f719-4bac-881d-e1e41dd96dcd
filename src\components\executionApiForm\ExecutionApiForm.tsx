import React from 'react';
import { Box } from '@mantine/core';
import classes from './ApiConfigSession.module.css';
import { ApiMethodUrlInput } from './ApiMethodUrlInput';
import { ApiConfigTabs } from './apiTabs/ApiConfigTabs';

interface ExecutionApiFormProps {
  isViewMode: boolean;
  variableNames?: string[];
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

const ExecutionApiForm: React.FC<ExecutionApiFormProps> = ({ isViewMode, registerEditor, variableNames }) => {
  return (
    <Box className={classes.apiConfigContainer}>
      {/* Method and URL Row */}
      <ApiMethodUrlInput isViewMode={isViewMode} executionParams={variableNames} registerEditor={registerEditor} />

      {/* API Config Tabs */}
      <ApiConfigTabs isViewMode={isViewMode} variableNames={variableNames} registerEditor={registerEditor} />
    </Box>
  );
};

export default ExecutionApiForm;
