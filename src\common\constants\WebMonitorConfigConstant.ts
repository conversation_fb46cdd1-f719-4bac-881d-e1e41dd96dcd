import { DEFAULT_LOCALE_EN } from 'cron-job/LocaleSelect';

export enum WebMonitorConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  VIEW = 'VIEW',
}

export enum MonitorTypeEnum {
  DISCOVER = 'DISCOVER',
  LOGIN = 'LOGIN',
}

export const MONITOR_TYPE_LABEL: { [key in MonitorTypeEnum]: string } = {
  [MonitorTypeEnum.DISCOVER]: 'Discover',
  [MonitorTypeEnum.LOGIN]: 'Login',
};

export enum BrowserEnum {
  CHROME = 'CHROME',
  FIREFOX = 'FIREFOX',
}

export const BROWSER_LABEL: { [key in BrowserEnum]: string } = {
  [BrowserEnum.CHROME]: 'Chrome',
  [BrowserEnum.FIREFOX]: 'Firefox',
};

export enum FindElementByEnum {
  XPATH = 'XPATH',
  CSS_SELECTOR = 'CSS_SELECTOR',
  ID = 'ID',
  NAME = 'NAME',
}

export const FIND_ELEMENT_BY_LABEL: { [key in FindElementByEnum]: string } = {
  [FindElementByEnum.XPATH]: 'XPATH',
  [FindElementByEnum.CSS_SELECTOR]: 'CSS SELECTOR',
  [FindElementByEnum.ID]: 'ID',
  [FindElementByEnum.NAME]: 'NAME',
};

export enum ActionTypeEnum {
  CLICK = 'CLICK',
  DOUBLE_CLICK = 'DOUBLE_CLICK',
  SEND_KEY = 'SEND_KEY',
  CLEAR_INPUT = 'CLEAR_INPUT',
  SWITCH_FRAME = 'SWITCH_FRAME',
  SWITCH_TO_DEFAULT_FRAME = 'SWITCH_TO_DEFAULT_FRAME',
  HOVER = 'HOVER',
  SELECT_FROM_DROPDOWN = 'SELECT_FROM_DROPDOWN',
  WAIT = 'WAIT',
  WAITING_FOR_ELEMENT = 'WAITING_FOR_ELEMENT',
  FIND_ELEMENT = 'FIND_ELEMENT',
  SWITCH_TO_POPUP = 'SWITCH_TO_POPUP',
  CLOSE_POPUP = 'CLOSE_POPUP',
  BACK_TO_MAIN = 'BACK_TO_MAIN',
  ALERT_ACCEPT = 'ALERT_ACCEPT',
  ALERT_DISMISS = 'ALERT_DISMISS',
  GO_TO_URL = 'GO_TO_URL',
}

export const ACTION_TYPE_LABEL: { [key in ActionTypeEnum]: string } = {
  [ActionTypeEnum.CLICK]: 'Click',
  [ActionTypeEnum.DOUBLE_CLICK]: 'Double click',
  [ActionTypeEnum.SEND_KEY]: 'Send key',
  [ActionTypeEnum.CLEAR_INPUT]: 'Clear input',
  [ActionTypeEnum.SWITCH_FRAME]: 'Switch to frame',
  [ActionTypeEnum.SWITCH_TO_DEFAULT_FRAME]: 'Switch to default frame',
  [ActionTypeEnum.HOVER]: 'Hover',
  [ActionTypeEnum.SELECT_FROM_DROPDOWN]: 'Select from dropdown',
  [ActionTypeEnum.WAIT]: 'Wait',
  [ActionTypeEnum.WAITING_FOR_ELEMENT]: 'Waiting for element',
  [ActionTypeEnum.FIND_ELEMENT]: 'Find element',
  [ActionTypeEnum.SWITCH_TO_POPUP]: 'Switch to popup',
  [ActionTypeEnum.CLOSE_POPUP]: 'Close popup',
  [ActionTypeEnum.BACK_TO_MAIN]: 'Back to main page',
  [ActionTypeEnum.ALERT_ACCEPT]: 'Accept alert',
  [ActionTypeEnum.ALERT_DISMISS]: 'Dismiss alert',
  [ActionTypeEnum.GO_TO_URL]: 'Go to url',
};

export const monthOptions = DEFAULT_LOCALE_EN.months.map((label, index) => ({
  value: (index + 1).toString(),
  label,
}));

export const weekOptions = DEFAULT_LOCALE_EN.weekDays.map((label, index) => ({
  value: (index + 1).toString(),
  label,
}));

export const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  value: `${i + 1}`,
  label: `${i + 1}`,
}));

export const hourOptions = Array.from({ length: 24 }, (_, i) => ({
  value: `${i}`,
  label: `${i}`,
}));

export const minuteOptions = Array.from({ length: 60 }, (_, i) => ({
  value: `${i}`,
  label: `${i}`,
}));

export const numberofRetryOptions = Array.from({ length: 4 }, (_, i) => ({
  value: `${i}`,
  label: `${i}`,
}));
