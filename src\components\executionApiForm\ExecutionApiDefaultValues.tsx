import { MethodTypeEnum, HttpVersionEnum, AuthTypeEnum, BodyTypeEnum } from '@common/constants/ExecutionConstants';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';

export const getDefaultApiInfo = (): ExecutionApiInfoModel => ({
  url: '',
  method: MethodTypeEnum.GET,
  httpVersion: HttpVersionEnum.HTTP1x,
  enableSsl: false,
  executionParams: [],
  authentication: {
    authType: AuthTypeEnum.NONE,
    token: '',
    username: '',
    password: '',
  },
  body: {
    bodyType: BodyTypeEnum.NONE,
    contentType: undefined,
    bodyRaw: '',
    formUrlEncoded: [{ key: '', value: '', description: '', enable: false }],
  },
  headers: [{ key: '', value: '', description: '', enable: false }],
  params: [{ key: '', value: '', description: '', enable: false }],
});
