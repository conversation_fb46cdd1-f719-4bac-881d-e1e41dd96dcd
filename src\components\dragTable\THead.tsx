import React from 'react';
import { ActionIcon, Flex, Table as MantineTable } from '@mantine/core';
import { THeadProps } from './Types';
import { SortType } from '@common/constants/SortType';
import { IconCaretUpDownFilled, IconCaretUpFilled, IconCaretDownFilled } from '@tabler/icons-react';

const SortIcon = ({ columnId, direction, sortBy }: { columnId: string; sortBy: string; direction: SortType }) => {
  if (columnId !== sortBy) {
    return <IconCaretUpDownFilled style={{ width: '55%', height: '55%' }} />;
  }
  if (direction === SortType.ASC) {
    return <IconCaretUpFilled style={{ width: '55%', height: '55%' }} />;
  }
  return <IconCaretDownFilled style={{ width: '55%', height: '55%' }} />;
};

function THead<T extends object>({ column, customProps, sortEffect }: THeadProps<T>) {
  return (
    <MantineTable.Th p='xs' {...customProps?.th} {...column.customProps?.th} w={column.width}>
      <Flex
        align='center'
        style={{
          position: 'relative',
          padding: 0,
          margin: 0,
        }}>
        <span style={{ flex: 1, textAlign: column.textAlign }}>{column.title}</span>
        {column.sortable && (
          <ActionIcon
            variant='transparent'
            style={{ position: 'absolute', right: '0' }}
            onClick={() => {
              if (column.id === sortEffect?.sortBy) {
                sortEffect?.onChange(column.id, sortEffect?.direction === SortType.ASC ? SortType.DESC : SortType.ASC);
              } else {
                sortEffect?.onChange(column.id, SortType.ASC);
              }
            }}>
            <SortIcon columnId={column.id} sortBy={sortEffect?.sortBy || ''} direction={sortEffect?.direction || SortType.ASC} />
          </ActionIcon>
        )}
      </Flex>
    </MantineTable.Th>
  );
}

export default THead;
