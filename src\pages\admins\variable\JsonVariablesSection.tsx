import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Box, Flex, SimpleGrid, Stack } from '@mantine/core';
import { KanbanInput, KanbanCheckbox, KanbanButton } from 'kanban-design-system';
import { MAX_CHARACTER_NAME_LENGTH, MAX_JSON_PATH_LENGTH } from '@common/constants/ValidationConstant';
import { IconTrash, IconPlus } from '@tabler/icons-react';

interface Props {
  form: UseFormReturn<any>;
  readonly: boolean;
  fields: any[];
  append: (value: any) => void;
  remove: (index: number) => void;
}

const JsonVariablesSection = ({ append, fields, form, readonly, remove }: Props) => {
  const { control } = form;

  return (
    <Stack gap='sm'>
      <Box>
        <strong>JSON Variable</strong>
      </Box>
      {fields.map((field, index) => (
        <SimpleGrid key={field.id} cols={3} spacing='sm'>
          <Controller
            name={`variableDetails.${index}.name`}
            control={control}
            render={({ field: nameField }) => (
              <KanbanInput
                label='Name'
                required
                {...nameField}
                maxLength={MAX_CHARACTER_NAME_LENGTH}
                disabled={readonly}
                onBlur={(e) => {
                  const value = e.target.value.replace(/\s/g, '');
                  nameField.onChange(value);
                  nameField.onBlur();
                }}
              />
            )}
          />
          <Controller
            name={`variableDetails.${index}.jsonPath`}
            control={control}
            render={({ field: jsonPathField }) => (
              <KanbanInput
                label='Position of json object'
                required
                {...jsonPathField}
                maxLength={MAX_JSON_PATH_LENGTH}
                placeholder='$.json.object'
                disabled={readonly}
              />
            )}
          />
          <Flex align='center' justify='center' gap='xs'>
            <Controller
              name={`variableDetails.${index}.hidden`}
              control={control}
              render={({ field: hiddenField }) => (
                <KanbanCheckbox label='Hidden' checked={hiddenField.value} onChange={hiddenField.onChange} disabled={readonly} />
              )}
            />
            {!readonly && (
              <KanbanButton variant='subtle' color='red' size='xs' onClick={() => remove(index)} disabled={fields.length <= 1}>
                <IconTrash size='1rem' />
              </KanbanButton>
            )}
          </Flex>
        </SimpleGrid>
      ))}
      {!readonly && (
        <Box>
          <KanbanButton
            variant='outline'
            leftSection={<IconPlus size='1rem' />}
            onClick={() => append({ id: undefined, name: '', jsonPath: '', hidden: false })}>
            Add variable
          </KanbanButton>
        </Box>
      )}
    </Stack>
  );
};

export default JsonVariablesSection;
