import { ComboboxItem } from '@mantine/core';

export enum DATABASE_ORACLE_CONNECT_TYPE {
  SID = 'SID',
  SERVICE_NAME = 'SERVICE_NAME',
}

export enum DATABASE_TYPE {
  ORACLE = 'ORACLE',
  Microsoft_SQL = 'Microsoft_SQL',
}

export const optionDatabaseTypes: ComboboxItem[] = [
  {
    label: 'Oracle',
    value: DATABASE_TYPE.ORACLE,
  },
  {
    label: 'Microsoft SQL',
    value: DATABASE_TYPE.Microsoft_SQL,
  },
];
