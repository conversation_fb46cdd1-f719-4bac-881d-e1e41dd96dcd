import React, { useEffect } from 'react';
import { Box, Flex } from '@mantine/core';
import { Controller, useFieldArray, useWatch, useFormContext } from 'react-hook-form';
import { KanbanTabs, KanbanText, KanbanSelect, KanbanSwitch, KanbanButton } from 'kanban-design-system';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import {
  AuthTypeEnum,
  BodyTypeEnum,
  ContentTypeEnum,
  CONTENT_TYPE_OPTIONS,
  getContentTypeValue,
  getFormUrlencodedContentType,
  HTTP_VERSION_OPTIONS,
  optionAuthTypes,
} from '@common/constants/ExecutionConstants';
import ParamAwareInput from './ParamAwareInput';
import { ParamAwareTextarea } from './ParamAwareTextarea';
import classes from '../ApiConfigSession.module.css';
import { generateAuthHeader, upsertAutoHeader } from '@common/utils/ExecutionApiUtils';
import { BodyTypeSelector } from './BodyTypeSelector';
import { RenderTable } from './TableRenderer';
import {
  EXECUTION_API_PASSWORD_MAX_LENGTH,
  EXECUTION_API_TOKEN_MAX_LENGTH,
  EXECUTION_API_USERNAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import { EXECUTION_API_AUTHORIZATION, EXECUTION_API_CONTENT_TYPE } from '@pages/admins/executionConfig/Constants';
import { beautifyRaw } from '@common/utils/BeautifyUtils';

interface ApiConfigTabsProps {
  isViewMode: boolean;
  variableNames?: string[];
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

export const ApiConfigTabs: React.FC<ApiConfigTabsProps> = ({ isViewMode, registerEditor, variableNames }) => {
  const form = useFormContext<{ apiInfo?: ExecutionApiInfoModel }>();
  const { control, getValues, setFocus, setValue } = form;

  const paramsFA = useFieldArray({ control, name: 'apiInfo.params' });
  const headersFA = useFieldArray({ control, name: 'apiInfo.headers' });
  const formFA = useFieldArray({ control, name: 'apiInfo.body.formUrlEncoded' });

  // Watch form values to display current data
  const authType = useWatch({ control, name: 'apiInfo.authentication.authType' });
  const authToken = useWatch({ control, name: 'apiInfo.authentication.token' });
  const authUsername = useWatch({ control, name: 'apiInfo.authentication.username' });
  const authPassword = useWatch({ control, name: 'apiInfo.authentication.password' });
  const bodyType = useWatch({ control, name: 'apiInfo.body.bodyType' });
  const selectedContentType = useWatch({ control, name: 'apiInfo.body.contentType' });

  useEffect(() => {
    if (isViewMode) {
      return;
    }

    const authValue = generateAuthHeader(authType, {
      token: authToken,
      username: authUsername,
      password: authPassword,
    });

    if (authValue) {
      upsertAutoHeader({
        key: EXECUTION_API_AUTHORIZATION,
        value: authValue,
        headersFA,
        setValue,
      });
    } else {
      const existing = headersFA.fields.findIndex((h) => h.key?.toLowerCase() === EXECUTION_API_AUTHORIZATION.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [authType, authToken, authUsername, authPassword, isViewMode, headersFA, setValue]);

  useEffect(() => {
    if (isViewMode) {
      return;
    }

    let contentTypeValue: string = '';

    if (bodyType === BodyTypeEnum.RAW && selectedContentType) {
      contentTypeValue = getContentTypeValue(selectedContentType);
    } else if (bodyType === BodyTypeEnum.URLENCODED) {
      contentTypeValue = getFormUrlencodedContentType();
    }

    if (contentTypeValue !== '') {
      upsertAutoHeader({
        key: EXECUTION_API_CONTENT_TYPE,
        value: contentTypeValue,
        headersFA,
        setValue,
      });
    } else {
      const existing = headersFA.fields.findIndex((h) => h.key?.toLowerCase() === EXECUTION_API_CONTENT_TYPE.toLowerCase() && h.autoGenerated);
      if (existing >= 0) {
        headersFA.remove(existing);
      }
    }
  }, [bodyType, selectedContentType, isViewMode, headersFA, setValue]);

  return (
    <KanbanTabs
      configs={{ defaultValue: 'params', classNames: { root: classes.tabsRoot, list: classes.tabsList } }}
      tabs={{
        params: {
          title: 'Params',
          content: (
            <RenderTable
              fa={paramsFA}
              control={control}
              namePrefix='apiInfo.params'
              isViewMode={isViewMode}
              executionParams={variableNames}
              getValues={getValues}
              setValue={setValue}
              registerEditor={registerEditor}
              setFocus={setFocus}
            />
          ),
        },
        auth: {
          title: EXECUTION_API_AUTHORIZATION,
          content: (
            <Box className={classes.bodyContainer}>
              {/* Type + Token/Username Row */}
              <Box className={classes.authInlineContainer}>
                <Box className={classes.authTypeInlineSection}>
                  <KanbanText fw={500} size='sm' className={classes.fieldLabel}>
                    Type
                  </KanbanText>
                  <Controller
                    name={'apiInfo.authentication.authType'}
                    control={control}
                    render={({ field }) => (
                      <KanbanSelect
                        {...field}
                        value={field.value}
                        disabled={isViewMode}
                        data={Object.values(optionAuthTypes)}
                        className={classes.authTypeSelect}
                        size='sm'
                      />
                    )}
                  />
                </Box>

                {/* Right side: Token or Username */}
                <Box className={classes.authFieldInlineSection}>
                  {authType === AuthTypeEnum.NONE && (
                    <Box className={classes.noAuthContainer}>
                      <KanbanText fw={600} size='lg' className={classes.noAuthTitle}>
                        No Auth
                      </KanbanText>
                      <KanbanText size='sm' c='dimmed' className={classes.noAuthDescription}>
                        This request does not use any authorization.
                      </KanbanText>
                    </Box>
                  )}
                  {authType === AuthTypeEnum.BEARER && (
                    <Box className={classes.authFieldInlineContainer}>
                      <KanbanText fw={500} size='sm' className={classes.fieldLabel}>
                        Token
                      </KanbanText>
                      <Controller
                        name={'apiInfo.authentication.token'}
                        control={control}
                        render={({ field }) => (
                          <ParamAwareInput
                            ref={field.ref}
                            field={{ ...field, value: field.value ?? '' }}
                            disabled={isViewMode}
                            executionParams={variableNames}
                            maxLength={EXECUTION_API_TOKEN_MAX_LENGTH}
                            registerEditor={registerEditor}
                          />
                        )}
                      />
                    </Box>
                  )}

                  {authType === AuthTypeEnum.BASIC && (
                    <>
                      <Box className={classes.authFieldInlineContainer}>
                        <KanbanText fw={500} size='sm' className={classes.fieldLabel}>
                          Username
                        </KanbanText>
                        <Controller
                          name={'apiInfo.authentication.username'}
                          control={control}
                          render={({ field }) => (
                            <ParamAwareInput
                              ref={field.ref}
                              field={{ ...field, value: field.value ?? '' }}
                              disabled={isViewMode}
                              executionParams={variableNames}
                              maxLength={EXECUTION_API_USERNAME_MAX_LENGTH}
                              registerEditor={registerEditor}
                            />
                          )}
                        />
                      </Box>

                      <Box className={classes.authFieldInlineContainer}>
                        <KanbanText fw={500} size='sm' className={classes.fieldLabel}>
                          Password
                        </KanbanText>
                        <Controller
                          name={'apiInfo.authentication.password'}
                          control={control}
                          render={({ field }) => (
                            <ParamAwareInput
                              ref={field.ref}
                              field={{ ...field, value: field.value ?? '' }}
                              disabled={isViewMode}
                              executionParams={variableNames}
                              maxLength={EXECUTION_API_PASSWORD_MAX_LENGTH}
                              registerEditor={registerEditor}
                            />
                          )}
                        />
                      </Box>
                    </>
                  )}
                </Box>
              </Box>
            </Box>
          ),
        },
        headers: {
          title: 'Headers',
          content: (
            <RenderTable
              fa={headersFA}
              control={control}
              namePrefix={'apiInfo.headers'}
              isViewMode={isViewMode}
              executionParams={variableNames}
              getValues={getValues}
              setValue={setValue}
              registerEditor={registerEditor}
              setFocus={setFocus}
            />
          ),
        },

        body: {
          title: 'Body',
          content: (
            <Box className={classes.bodyContainer}>
              <Flex align='flex-end' gap='md' wrap='wrap'>
                <BodyTypeSelector
                  namePrefix={'apiInfo'}
                  control={control}
                  setValue={setValue}
                  getValues={getValues}
                  isViewMode={isViewMode}
                  headersFA={headersFA}
                />
                {bodyType === BodyTypeEnum.RAW && (
                  <Flex align='center' gap='sm' className={classes.rawControlsInline}>
                    <Controller
                      name={'apiInfo.body.contentType'}
                      control={control}
                      render={({ field }) => (
                        <KanbanSelect
                          {...field}
                          disabled={isViewMode}
                          data={CONTENT_TYPE_OPTIONS}
                          className={classes.contentTypeSelectInline}
                          size='xs'
                          allowDeselect={false}
                          w={100}
                        />
                      )}
                    />
                    <KanbanButton
                      size='xs'
                      variant='light'
                      className={classes.beautifyBtn}
                      disabled={isViewMode}
                      onClick={async () => {
                        const rawContent = getValues('apiInfo.body.bodyRaw');
                        const contentType = getValues('apiInfo.body.contentType') || selectedContentType || ContentTypeEnum.TEXT;
                        const formatted = await beautifyRaw(String(rawContent ?? ''), contentType);
                        setValue('apiInfo.body.bodyRaw', formatted, { shouldDirty: true });
                      }}>
                      Pretty
                    </KanbanButton>
                  </Flex>
                )}
              </Flex>
              {bodyType === BodyTypeEnum.RAW && (
                <Box>
                  <Controller
                    name={'apiInfo.body.bodyRaw'}
                    control={control}
                    render={({ field }) => (
                      <ParamAwareTextarea
                        field={{ ...field, value: String(field.value ?? '') }}
                        disabled={isViewMode}
                        executionParams={variableNames}
                        contentType={selectedContentType}
                      />
                    )}
                  />
                </Box>
              )}
              {bodyType === BodyTypeEnum.URLENCODED && (
                <Box>
                  <RenderTable
                    fa={formFA}
                    control={control}
                    namePrefix={'apiInfo.body.formUrlEncoded'}
                    isViewMode={isViewMode}
                    executionParams={variableNames}
                    getValues={getValues}
                    setValue={setValue}
                    registerEditor={registerEditor}
                    setFocus={setFocus}
                  />
                </Box>
              )}
            </Box>
          ),
        },
        settings: {
          title: 'Settings',
          content: (
            <Box className={classes.bodyContainer}>
              <Box className={classes.settingsGroup}>
                <Box>
                  <KanbanText className={classes.settingsLabel}>HTTP Version</KanbanText>
                  <KanbanText className={classes.settingsNote}>Select the HTTP version to use for sending the request</KanbanText>
                </Box>
                <Controller
                  name={'apiInfo.httpVersion'}
                  control={control}
                  render={({ field }) => (
                    <KanbanSelect
                      {...field}
                      value={field.value}
                      disabled={isViewMode}
                      data={HTTP_VERSION_OPTIONS}
                      size='sm'
                      style={{ width: '120px' }}
                    />
                  )}
                />
              </Box>
              <Box className={classes.settingsGroup}>
                <Box>
                  <KanbanText className={classes.settingsLabel}>Enable SSL certificate verification</KanbanText>
                  <KanbanText className={classes.settingsNote}>
                    Verify SSL certificates when sending a request. Verification failures will result in the request being aborted.
                  </KanbanText>
                </Box>
                <Controller
                  name='apiInfo.enableSsl'
                  control={control}
                  render={({ field }) => (
                    <KanbanSwitch disabled={isViewMode} checked={field.value} onChange={(event) => field.onChange(event.currentTarget.checked)} />
                  )}
                />
              </Box>
            </Box>
          ),
        },
      }}
    />
  );
};
