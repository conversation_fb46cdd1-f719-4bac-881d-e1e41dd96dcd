import { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import React from 'react';
import { Box, Card, Group } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanText } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { VariableApi } from '@api/VariableApi';
import classes from '../ApiConfigSession.module.css';
import keyboardKey from 'keyboard-key';

export interface ParamListProps extends SuggestionProps {}

export interface ParamListActions {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
}

export const ParamList = forwardRef<ParamListActions, ParamListProps>(({ command, query }, ref) => {
  const { data: varData } = useFetch(VariableApi.findAll(), { showLoading: false });
  const variables = useMemo(
    () =>
      varData?.data?.map((v) => ({
        key: v.name,
      })) || [],
    [varData],
  );

  const [localVariableName, setLocalVariableName] = useState('');

  const filteredVariables = variables.filter((v) => v.key.toLowerCase().includes(query?.toLowerCase?.() || ''));

  const [hoverIndex, setHoverIndex] = useState(0);

  const handleSelectVariable = (key: string) => {
    command({ id: key, label: key });
  };

  const handleCreateVariable = () => {
    const name = localVariableName.trim();
    if (name) {
      command({ id: name, label: name });
      setLocalVariableName('');
    }
  };

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      const key = keyboardKey.getCode(event);
      switch (key) {
        case keyboardKey.ArrowDown:
          setHoverIndex((prev) => Math.min(prev + 1, filteredVariables.length - 1));
          return true;
        case keyboardKey.ArrowUp:
          setHoverIndex((prev) => Math.max(prev - 1, 0));
          return true;
        case keyboardKey.Enter:
          if (filteredVariables[hoverIndex]) {
            handleSelectVariable(filteredVariables[hoverIndex].key);
          } else if (localVariableName.trim()) {
            handleCreateVariable();
          }
          return true;
        default:
          return false;
      }
    },
  }));

  return (
    <Card shadow='md' radius='md'>
      <Box bg='var(--mantine-color-white-9)' onClick={(e) => e.stopPropagation()}>
        <Box
          p='md'
          style={{
            borderBottom: '1px solid var(--mantine-color-gray-2)',
            backgroundColor: 'var(--mantine-color-gray-0)',
          }}>
          <KanbanText fw={600} size='sm' mb='xs' style={{ textAlign: 'center' }}>
            ⋮⋮ Select Variable ⋮⋮
          </KanbanText>
          <KanbanInput placeholder='Search variable name' value={query} autoFocus size='sm' />
        </Box>

        <Box h={200} style={{ overflowY: 'auto' }}>
          {filteredVariables.length > 0 ? (
            filteredVariables.map((variable, index) => (
              <Box
                key={variable.key}
                p='sm'
                className={classes.variableItem}
                onClick={() => handleSelectVariable(variable.key)}
                style={{
                  cursor: 'pointer',
                  borderBottom: '1px solid var(--mantine-color-gray-1)',
                  backgroundColor: index === hoverIndex ? 'var(--mantine-color-gray-1)' : 'transparent',
                }}>
                <KanbanText size='sm' fw={500}>
                  {variable.key}
                </KanbanText>
              </Box>
            ))
          ) : (
            <Box p='sm'>
              <KanbanText size='sm' c='dimmed'>
                No variables found
              </KanbanText>
            </Box>
          )}
        </Box>

        <Box p='md' style={{ borderTop: '1px solid var(--mantine-color-gray-2)' }}>
          <KanbanText fw={600} size='sm' mb='xs'>
            Create local variable
          </KanbanText>
          <Group gap='xs'>
            <KanbanInput
              placeholder='Enter local variable name'
              value={localVariableName}
              onChange={(e) => setLocalVariableName(e.target.value)}
              size='sm'
              flex={1}
              onKeyDown={(e) => {
                if (keyboardKey.Enter === keyboardKey.getCode(e)) {
                  handleCreateVariable();
                }
              }}
            />
            <KanbanButton size='sm' onClick={handleCreateVariable} disabled={!localVariableName.trim()}>
              Create
            </KanbanButton>
          </Group>
        </Box>
      </Box>
    </Card>
  );
});

ParamList.displayName = 'ParamList';
