import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { z } from 'zod';
import { VariableSchema } from './Variable';
import { ExecutionApiInfoSchema } from './ExecutionApiInfo';

export const ExecutionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  type: z.nativeEnum(ExecutionTypeEnum),
  databaseConnectionId: z.number().optional(),
  executionGroupId: z.string(),
  executionGroupName: z.string().optional(),
  script: z.string().optional(),
  variables: z
    .array(
      VariableSchema.extend({
        id: z.string().optional(),
        value: z.string().optional(),
        hidden: z.boolean().optional(),
      }),
    )
    .optional(),
  apiInfo: ExecutionApiInfoSchema.optional(),
});

export type Execution = z.infer<typeof ExecutionSchema>;
