import React from 'react';
import { Code, Flex, ScrollAreaAutosize, Stack } from '@mantine/core';
import { ApiExecuteResponse } from '@core/schema/ExecuteApiResponseSchema';
import ReactJson from 'react-json-view';
import { KanbanText } from 'kanban-design-system';
import { formatDuration } from '@common/utils/DateUtils';
import { parseJsonContent } from '@common/utils/BeautifyUtils';

interface Props {
  result: ApiExecuteResponse;
}

const ApiResult = ({ result }: Props) => {
  const { body, contentType, durationMillis, statusCode, statusText } = result;

  const jsonContent = parseJsonContent(body ?? '');

  const displayContentType = contentType || 'text/plain';

  return (
    <Stack>
      <Flex justify='end' p='xs' bg='gray.1' gap='md' c='dimmed'>
        <KanbanText>{displayContentType}</KanbanText>
        <KanbanText c={statusCode > 400 ? 'red' : 'green'}>
          {statusCode} {statusText}
        </KanbanText>
        <KanbanText>{formatDuration(durationMillis)}</KanbanText>
      </Flex>

      <ScrollAreaAutosize>
        {jsonContent ? (
          <ReactJson
            src={jsonContent}
            indentWidth={1}
            name={false}
            enableClipboard={false}
            sortKeys
            collapsed={false}
            displayObjectSize={false}
            displayDataTypes={false}
            style={{
              fontSize: 13,
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
            }}
          />
        ) : (
          <Code
            block
            style={{
              whiteSpace: 'pre-wrap',
              fontSize: 13,
              fontFamily: 'monospace',
              margin: 0,
            }}>
            {body}
          </Code>
        )}
      </ScrollAreaAutosize>
    </Stack>
  );
};

export default ApiResult;
