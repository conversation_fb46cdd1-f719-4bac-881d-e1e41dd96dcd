import React, { useId, useState } from 'react';
import { ActionIcon, FileInput, Flex, InputLabel, Pill, Tooltip } from '@mantine/core';
import { FileStorage } from '@core/schema/FileStorage';
import { NotificationError } from '@common/utils/NotificationUtils';
import { IconDownload, IconPaperclip, IconX } from '@tabler/icons-react';
import { getConfigs } from '@core/configs/Configs';
import useMutate from '@core/hooks/useMutate';
import { EmailTemplateApi } from '@api/EmailTemplateApi';
import { saveAs } from 'file-saver';
import { KanbanText } from 'kanban-design-system';
interface MultiFileInputProps {
  onFilesChange?: (newFiles: File[], remainingInitialFiles: FileStorage[]) => void;
  fileStorages: FileStorage[];
  files?: File[];
  isViewMode?: boolean;
}

const MultiFileInput: React.FC<MultiFileInputProps> = ({ files, fileStorages, isViewMode, onFilesChange }) => {
  const [newFiles, setNewFiles] = useState<File[]>(files || []);
  const idFileInput = useId();
  const [fileName, setFileName] = useState<string | undefined>(); // file which user want to download
  const handleFilesSelected = (files: File[] | null) => {
    if (isViewMode) {
      return;
    }

    const maxFileSize = getConfigs().maxFileAttachSize;
    const maxTotalSize = getConfigs().maxEmailAttachSize; // 20 MB in bytes

    if (files) {
      const validFiles: File[] = [];
      const invalidFiles: File[] = [];

      const currentTotalSize = fileStorages.reduce((total, file) => total + file.size, 0);
      const newFilesSize = newFiles.reduce((total, file) => total + file.size, 0);

      const filesUploadSize = files.reduce((total, file) => total + file.size, 0);
      if (currentTotalSize + newFilesSize + filesUploadSize > maxTotalSize) {
        NotificationError({
          title: 'File Size Error',
          message: `The total file size exceeds the ${maxTotalSize / (1024 * 1024)} MB limit. Please select smaller files.`,
        });
        return;
      }

      files.forEach((file) => {
        if (file.size <= maxFileSize) {
          validFiles.push(file);
        } else {
          invalidFiles.push(file);
        }
      });

      const updatedNewFiles = [...newFiles, ...validFiles];
      setNewFiles(updatedNewFiles);
      onFilesChange?.(updatedNewFiles, fileStorages);

      if (invalidFiles.length > 0) {
        NotificationError({
          title: 'File Size Error',
          message: `The following files are larger than ${maxFileSize / (1024 * 1024)} MB and will not be added: ${invalidFiles.map((file) => file.name).join(', ')}`,
        });
      }
    }
  };

  const removeFile = (fileId: number, fileName?: string) => {
    if (isViewMode) {
      return;
    }
    const fileInInitialFiles = fileStorages.find((file) => file.id === fileId);
    if (fileInInitialFiles) {
      const updatedInitialFiles = fileStorages.filter((file) => file.id !== fileId);
      onFilesChange?.(newFiles, updatedInitialFiles);
    } else if (fileName) {
      const updatedNewFiles = newFiles.filter((file) => file.name !== fileName);
      setNewFiles(updatedNewFiles);
      onFilesChange?.(updatedNewFiles, fileStorages);
    }
  };
  const { mutate: downloadFile } = useMutate(EmailTemplateApi.download, {
    successNotification: { message: 'Export file successfully' },
    onSuccess: (response) => {
      if (response instanceof Blob) {
        saveAs(response, fileName);
      }
    },
    showLoading: false,
  });

  return (
    <Flex align={'center'} my='xs' justify={'left'} gap='sm'>
      <InputLabel size='xs' htmlFor={idFileInput} style={{ display: 'flex', alignItems: 'center', cursor: isViewMode ? 'not-allowed' : 'pointer' }}>
        <IconPaperclip
          style={{
            border: '1px solid var(--mantine-color-primary-2)',
            padding: '0.25rem',
            color: 'var(--mantine-color-primary-7)',
            borderRadius: '0.25rem',
            boxSizing: 'content-box',
          }}
        />
        {!isViewMode && <FileInput id={idFileInput} multiple onChange={handleFilesSelected} display={'none'} />}
      </InputLabel>

      <Flex align={'center'} my='xs' justify={'left'} gap='xs' wrap={'wrap'}>
        {fileStorages.map((file, index) => (
          <Pill size='md' bg='white' color='primary' bd='1px solid var(--mantine-color-primary-2)' key={index}>
            <Flex align='center' gap='xs'>
              <KanbanText>{file.name}</KanbanText>
              <Flex align={'center'}>
                <Tooltip label='Download' withArrow>
                  <ActionIcon
                    size='sm'
                    variant='subtle'
                    onClick={(e) => {
                      e.stopPropagation();
                      setFileName(file.name);
                      downloadFile(file.id);
                    }}>
                    <IconDownload size={16} />
                  </ActionIcon>
                </Tooltip>

                {!isViewMode && (
                  <Tooltip label='Remove' withArrow>
                    <ActionIcon
                      size='sm'
                      variant='subtle'
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFile(file.id);
                      }}>
                      <IconX />
                    </ActionIcon>
                  </Tooltip>
                )}
              </Flex>
            </Flex>
          </Pill>
        ))}
        {newFiles.map((file, index) => (
          <Pill size='md' bg='white' color='black' bd='1px solid var(--mantine-color-primary-2)' key={index}>
            <Flex align='center' gap='xs'>
              {file.name}
              {!isViewMode && (
                <Tooltip label='Remove' withArrow>
                  <ActionIcon
                    size='sm'
                    variant='subtle'
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(0, file.name);
                    }}>
                    <IconX />
                  </ActionIcon>
                </Tooltip>
              )}
            </Flex>
          </Pill>
        ))}
      </Flex>
    </Flex>
  );
};
export default MultiFileInput;
