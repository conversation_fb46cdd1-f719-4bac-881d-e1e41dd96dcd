import { CustomObjectSearchRequest } from '@api/Type';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { QueryRuleGroupType, QueryRuleType } from '@core/schema/RuleGroupCondition';
import { ApplicationPaginationRequest } from '@models/ApplicationModel';
import { ServicePaginationRequest } from '@models/ServiceModel';
import { SortType } from '@common/constants/SortType';
import { AutoTriggerActionConfigModel } from '@models/AutoTriggerActionConfigModel';

export enum AutoTriggerConfigOperatorEnum {
  IS = QueryBuilderOperatorEnum.IS,
  IS_NOT = QueryBuilderOperatorEnum.IS_NOT,
  IS_ONE_OF = QueryBuilderOperatorEnum.IS_ONE_OF,
  IS_NOT_ONE_OF = QueryBuilderOperatorEnum.IS_NOT_ONE_OF,
  CONTAINS = QueryBuilderOperatorEnum.CONTAINS,
  DOES_NOT_CONTAIN = QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
}

export enum AutoTriggerConfigAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  VIEW = 'VIEW',
}
export const DEFAULT_TIME_LAST_TRIGGER_VALUE = 7200;

export const DEFAULT_RULE: QueryRuleType = { field: 'content', operator: QueryBuilderOperatorEnum.CONTAINS, value: '' };

export const DEFAULT_GROUP_CONDITION: QueryRuleGroupType = {
  combinator: QueryBuilderCombinatorEnum.AND,
  rules: [DEFAULT_RULE],
};

export const DEFAULT_APPLICATION_PAGINATION_REQUEST: ApplicationPaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  name: '',
  withDeleted: false,
};

export const DEFAULT_SERVICE_PAGINATION_REQUEST: ServicePaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  withDeleted: false,
};

export const DEFAULT_CUSTOM_OBJECT_SEARCH_REQUEST: CustomObjectSearchRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortOrder: SortType.ASC,
  sortBy: 'name',
  size: 10000,
  withDeleted: false,
};

export enum AutoTriggerActionConfigTypeEnum {
  CONDITION = 'CONDITION',
  TIME = 'TIME',
}

export const TRIGGER_TYPE_LABEL: { [key in AutoTriggerActionConfigTypeEnum]: string } = {
  [AutoTriggerActionConfigTypeEnum.CONDITION]: 'Condition',
  [AutoTriggerActionConfigTypeEnum.TIME]: 'Time',
};

export const DEFAULT_FORM_VALUE: AutoTriggerActionConfigModel = {
  name: '',
  ruleGroup: DEFAULT_GROUP_CONDITION,
  applications: [],
  services: [],
  executions: [],
  timeSinceLastTrigger: DEFAULT_TIME_LAST_TRIGGER_VALUE,
  triggerType: AutoTriggerActionConfigTypeEnum.CONDITION,
  cronExpression: '* * * * *',
};

export const TypeOptions = Object.values(AutoTriggerActionConfigTypeEnum).map((ele) => ({ label: TRIGGER_TYPE_LABEL[ele], value: ele }));
