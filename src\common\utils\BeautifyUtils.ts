import prettier from 'prettier/standalone';
import parserBabel from 'prettier/plugins/babel';
import parserHtml from 'prettier/plugins/html';
import parserXml from '@prettier/plugin-xml';
import { ContentTypeEnum } from '@common/constants/ExecutionConstants';

export async function beautifyRaw(content: string, contentType?: ContentTypeEnum): Promise<string> {
  if (!content || !content.trim()) {
    return content;
  }

  try {
    switch (contentType) {
      case ContentTypeEnum.JSON:
        return JSON.stringify(JSON.parse(content), null, 2);
      case ContentTypeEnum.JAVASCRIPT:
        return await prettier.format(content, {
          parser: 'babel',
          plugins: [parserBabel],
          tabWidth: 2,
        });

      case ContentTypeEnum.HTML:
        return await prettier.format(content, {
          parser: 'html',
          plugins: [parserHtml],
          tabWidth: 2,
        });

      case ContentTypeEnum.XML:
        return prettier.format(content, {
          parser: 'xml',
          plugins: [parserXml],
        });

      case ContentTypeEnum.TEXT:
      default:
        return content;
    }
  } catch (error) {
    console.warn('Failed to beautify content:', error);
    return content;
  }
}

export const parseJsonContent = (body: string) => {
  if (!body) {
    return null;
  }
  try {
    return JSON.parse(body);
  } catch {
    return null;
  }
};
