import React from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { Flex, Grid } from '@mantine/core';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import { FIND_ELEMENT_BY_LABEL, FindElementByEnum } from '@common/constants/WebMonitorConfigConstant';
import { WebMonitorConfigModel } from '@models/WebMonitorConfigModel';
import { CustomVariableInput } from './CustomVariableInput';

interface Props {
  form: UseFormReturn<WebMonitorConfigModel>;
  isViewMode?: boolean;
}

const AuthenticationSession = ({ form, isViewMode }: Props) => {
  const { control, getValues, setValue } = form;
  return (
    <Flex direction='column' gap='sm'>
      <form>
        <Grid gutter='md'>
          <Grid.Col span={2}>
            <Controller
              name='authActions.0.findElementBy'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  label='Find element by'
                  required
                  disabled={isViewMode}
                  data={Object.values(FindElementByEnum).map((key) => ({ value: key, label: FIND_ELEMENT_BY_LABEL[key] }))}
                  defaultValue={FindElementByEnum.XPATH}
                  allowDeselect={false}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={3}>
            <Controller
              name='authActions.0.identifier'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  disabled={isViewMode}
                  required
                  label='Username'
                  placeholder='Position of textbox username'
                  maxLength={5000}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={7}>
            <Controller
              name='authActions.0.value'
              control={control}
              rules={{ required: 'Value is required' }}
              render={({ field: { onChange }, fieldState }) => (
                <CustomVariableInput
                  label='Value'
                  disabled={!!isViewMode}
                  required={true}
                  maxLength={500}
                  value={getValues('authActions.0.value') || ''}
                  error={fieldState.error?.message}
                  onChange={(content: any, val: any) => {
                    onChange(val);
                    setValue('authActions.0.value', content);
                  }}
                />
              )}
            />
          </Grid.Col>
        </Grid>
        <Grid gutter='md'>
          <Grid.Col span={2}>
            <Controller
              name='authActions.1.findElementBy'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  label='Find element by'
                  required
                  disabled={isViewMode}
                  data={Object.values(FindElementByEnum).map((key) => ({ value: key, label: FIND_ELEMENT_BY_LABEL[key] }))}
                  defaultValue={FindElementByEnum.XPATH}
                  allowDeselect={false}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={3}>
            <Controller
              name='authActions.1.identifier'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  disabled={isViewMode}
                  required
                  label='Password'
                  placeholder='Position of textbox password'
                  maxLength={5000}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={7}>
            <Controller
              name='authActions.1.value'
              control={control}
              rules={{ required: 'Value is required' }}
              render={({ field: { onChange }, fieldState }) => (
                <CustomVariableInput
                  label='Value'
                  disabled={!!isViewMode}
                  required={true}
                  maxLength={500}
                  value={getValues('authActions.1.value') || ''}
                  error={fieldState.error?.message}
                  onChange={(content: any, val: any) => {
                    onChange(val);
                    setValue('authActions.1.value', content);
                  }}
                />
              )}
            />
          </Grid.Col>
        </Grid>

        <Grid gutter='md'>
          <Grid.Col span={2}>
            <Controller
              name='authActions.2.findElementBy'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanSelect
                  label='Find element by'
                  required
                  disabled={isViewMode}
                  data={Object.values(FindElementByEnum).map((key) => ({ value: key, label: FIND_ELEMENT_BY_LABEL[key] }))}
                  defaultValue={FindElementByEnum.XPATH}
                  allowDeselect={false}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={10}>
            <Controller
              name='authActions.2.identifier'
              control={control}
              render={({ field, fieldState }) => (
                <KanbanInput
                  disabled={isViewMode}
                  required
                  label='Button login'
                  placeholder='Position of button login'
                  maxLength={5000}
                  {...field}
                  error={fieldState.error?.message}
                />
              )}
            />
          </Grid.Col>
        </Grid>
      </form>
    </Flex>
  );
};

export default AuthenticationSession;
