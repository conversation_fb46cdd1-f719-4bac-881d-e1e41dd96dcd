import { EnumKey } from '@common/utils/Type';

export enum SysLogActionEnum {
  //  CUSTOM_OBJECT
  CREATE_CUSTOM_OBJECT = 'CREATE_CUSTOM_OBJECT',
  EDIT_CUSTOM_OBJECT = 'EDIT_CUSTOM_OBJECT',
  DELETE_CUSTOM_OBJECT = 'DELETE_CUSTOM_OBJECT',

  // USER_MANAGEMENT
  CREATE_USER = 'CREATE_USER',
  INACTIVE_USER = 'INACTIVE_USER',
  ACTIVE_USER = 'ACTIVE_USER',
  SET_ADMIN = 'SET_ADMIN',
  UNSET_ADMIN = 'UNSET_ADMIN',
  DELETE_USER = 'DELETE_USER',
  EDIT_USER = 'EDIT_USER',
  UPDATE_USER_ROLE = 'UPDATE_USER_ROLE',

  //ROLE_MANAGEMENT
  CREATE_ROLE = 'CREATE_ROLE',
  EDIT_ROLE = 'EDIT_ROLE',
  INACTIVE_ROLE = 'INACTIVE_ROLE',
  ACTIVE_ROLE = 'ACTIVE_ROLE',
  DELETE_ROLE = 'DELETE_ROLE',

  // INPUT_DATA
  CREATE_WEBHOOK_COLLECT = 'CREATE_WEBHOOK_COLLECT',
  EDIT_WEBHOOK_COLLECT = 'EDIT_WEBHOOK_COLLECT',
  DELETE_WEBHOOK_COLLECT = 'DELETE_WEBHOOK_COLLECT',
  CREATE_DATABASE_COLLECT = 'CREATE_DATABASE_COLLECT',
  EDIT_DATABASE_COLLECT = 'EDIT_DATABASE_COLLECT',
  DELETE_DATABASE_COLLECT = 'DELETE_DATABASE_COLLECT',
  ACTIVE_DATABASE_COLLECT = 'ACTIVE_DATABASE_COLLECT',
  INACTIVE_DATABASE_COLLECT = 'INACTIVE_DATABASE_COLLECT',
  CREATE_EMAIL_COLLECT = 'CREATE_EMAIL_COLLECT',
  EDIT_EMAIL_COLLECT = 'EDIT_EMAIL_COLLECT',
  DELETE_EMAIL_COLLECT = 'DELETE_EMAIL_COLLECT',
  ACTIVE_EMAIL_COLLECT = 'ACTIVE_EMAIL_COLLECT',
  INACTIVE_EMAIL_COLLECT = 'INACTIVE_EMAIL_COLLECT',

  // SERVICE_MANAGER
  CREATE_SERVICE = 'CREATE_SERVICE',
  DELETE_SERVICE = 'DELETE_SERVICE',
  EDIT_SERVICE = 'EDIT_SERVICE',

  // APPLICATION_MANAGER
  CREATE_APPLICATION = 'CREATE_APPLICATION',
  DELETE_APPLICATION = 'DELETE_APPLICATION',
  EDIT_APPLICATION = 'EDIT_APPLICATION',

  // PRIORITY_CONFIG
  CREATE_PRIORITY_CONFIG = 'CREATE_PRIORITY_CONFIG',
  EDIT_PRIORITY_CONFIG = 'EDIT_PRIORITY_CONFIG',
  DELETE_PRIORITY_CONFIG = 'DELETE_PRIORITY_CONFIG',
  CHANGE_ORDER_OF_PRIORITY_CONFIG = 'CHANGE_ORDER_OF_PRIORITY_CONFIG',

  // GROUP_ALERT_CONFIG
  CREATE_ALERT_GROUP_CONFIG = 'CREATE_ALERT_GROUP_CONFIG',
  DELETE_ALERT_GROUP_CONFIG = 'DELETE_ALERT_GROUP_CONFIG',
  EDIT_ALERT_GROUP_CONFIG = 'EDIT_ALERT_GROUP_CONFIG',
  CHANGE_ORDER_OF_ALERT_GROUP_CONFIG = 'CHANGE_ORDER_OF_ALERT_GROUP_CONFIG',
  ACTIVE_ALERT_GROUP_CONFIG = 'ACTIVE_ALERT_GROUP_CONFIG',
  INACTIVE_ALERT_GROUP_CONFIG = 'INACTIVE_ALERT_GROUP_CONFIG',

  // MAINTENANCE_TIME_CONFIG
  CREATE_MAINTENANCE_TIME_CONFIG = 'CREATE_MAINTENANCE_TIME_CONFIG',
  EDIT_MAINTENANCE_TIME_CONFIG = 'EDIT_MAINTENANCE_TIME_CONFIG',
  DELETE_MAINTENANCE_TIME_CONFIG = 'DELETE_MAINTENANCE_TIME_CONFIG',
  ACTIVE_MAINTENANCE_TIME_CONFIG = 'ACTIVE_MAINTENANCE_TIME_CONFIG',
  INACTIVE_MAINTENANCE_TIME_CONFIG = 'INACTIVE_MAINTENANCE_TIME_CONFIG',

  // MAINTENANCE_TIME_CONFIG
  CREATE_FILTER_CONFIG = 'CREATE_FILTER_CONFIG',
  EDIT_FILTER_CONFIG = 'EDIT_FILTER_CONFIG',
  DELETE_FILTER_CONFIG = 'DELETE_FILTER_CONFIG',
  ACTIVE_FILTER_CONFIG = 'ACTIVE_FILTER_CONFIG',
  INACTIVE_FILTER_CONFIG = 'INACTIVE_FILTER_CONFIG',

  // EMAIL_CONFIG
  CREATE_PARTNER = 'CREATE_PARTNER',
  EDIT_PARTNER = 'EDIT_PARTNER',
  DELETE_PARTNER = 'DELETE_PARTNER',
  CREATE_TEMPLATE = 'CREATE_TEMPLATE',
  EDIT_TEMPLATE = 'EDIT_TEMPLATE',
  DELETE_TEMPLATE = 'DELETE_TEMPLATE',
  CREATE_EMAIL_CONNECTION = 'CREATE_EMAIL_CONNECTION',
  EDIT_EMAIL_CONNECTION = 'EDIT_EMAIL_CONNECTION',
  ACTIVE_EMAIL_CONNECTION = 'ACTIVE_EMAIL_CONNECTION',
  INACTIVE_EMAIL_CONNECTION = 'INACTIVE_EMAIL_CONNECTION',
  DELETE_EMAIL_CONNECTION = 'DELETE_EMAIL_CONNECTION',

  // DATABASE_CONFIG
  CREATE_DATABASE_CONNECT = 'CREATE_DATABASE_CONNECT',
  EDIT_DATABASE_CONNECT = 'EDIT_DATABASE_CONNECT',
  ACTIVE_DATABASE_CONNECT = 'ACTIVE_DATABASE_CONNECT',
  INACTIVE_DATABASE_CONNECT = 'INACTIVE_DATABASE_CONNECT',
  CREATE_GROUP_QUERY = 'CREATE_GROUP_QUERY',
  EDIT_GROUP_QUERY = 'EDIT_GROUP_QUERY',
  DELETE_GROUP_QUERY = 'DELETE_GROUP_QUERY',
  CREATE_QUERY = 'CREATE_QUERY',
  EDIT_QUERY = 'EDIT_QUERY',
  DELETE_QUERY = 'DELETE_QUERY',
  //DATABASE THRESHOLD
  CREATE_DATABASE_THRESHOLD = 'CREATE_DATABASE_THRESHOLD',
  EDIT_DATABASE_THRESHOLD = 'EDIT_DATABASE_THRESHOLD',
  DELETE_DATABASE_THRESHOLD = 'DELETE_DATABASE_THRESHOLD',
  ACTIVE_DATABASE_THRESHOLD = 'ACTIVE_DATABASE_THRESHOLD',
  INACTIVE_DATABASE_THRESHOLD = 'INACTIVE_DATABASE_THRESHOLD',

  // SYSTEM
  DENY_ALERT_BY_WRONG_INFORMATION = 'DENY_ALERT_BY_WRONG_INFORMATION',
  DENY_ALERT_BY_FILTER_ALERT = 'DENY_ALERT_BY_FILTER_ALERT',
  MODIFY_ALERT = 'MODIFY_ALERT',
  SEND_TELEGRAM_FAILED = 'SEND_TELEGRAM_FAILED',
  DATABASE_THRESHOLD_ERROR = 'DATABASE_THRESHOLD_ERROR',
  DATABASE_THRESHOLD_FAILED = 'DATABASE_THRESHOLD_FAILED',

  // TRIGGER CONFIG
  DENY_TRIGGER = 'DENY_TRIGGER',
  ERROR_TRIGGER = 'ERROR_TRIGGER',
  CREATE_AUTO_TRIGGER_EXECUTION = 'CREATE_AUTO_TRIGGER_EXECUTION',
  EDIT_AUTO_TRIGGER_EXECUTION = 'EDIT_AUTO_TRIGGER_EXECUTION',
  INACTIVE_AUTO_TRIGGER_EXECUTION = 'INACTIVE_AUTO_TRIGGER_EXECUTION',
  ACTIVE_AUTO_TRIGGER_EXECUTION = 'ACTIVE_AUTO_TRIGGER_EXECUTION',
  DELETE_AUTO_TRIGGER_EXECUTION = 'DELETE_AUTO_TRIGGER_EXECUTION',

  //MONITOR WEB JOB
  SUCCESS_MONITOR_WEB_JOB = 'SUCCESS_MONITOR_WEB_JOB',
  LAST_ERROR_MONITOR_WEB_JOB = 'LAST_ERROR_MONITOR_WEB_JOB',
  ATTEMPT_ERROR_MONITOR_WEB_JOB = 'ATTEMPT_ERROR_MONITOR_WEB_JOB',
  FIRST_ERROR_MONITOR_WEB_JOB = 'FIRST_ERROR_MONITOR_WEB_JOB',

  //WEB MONITOR CONFIG
  CREATE_MONITOR_WEB_CONFIG = 'CREATE_MONITOR_WEB_CONFIG',
  EDIT_MONITOR_WEB_CONFIG = 'EDIT_MONITOR_WEB_CONFIG',
  DELETE_MONITOR_WEB_CONFIG = 'DELETE_MONITOR_WEB_CONFIG',
  ACTIVE_MONITOR_WEB_CONFIG = 'ACTIVE_MONITOR_WEB_CONFIG',
  INACTIVE_MONITOR_WEB_CONFIG = 'INACTIVE_MONITOR_WEB_CONFIG',
}

export enum SysLogFunctionEnum {
  CUSTOM_OBJECT = 'CUSTOM_OBJECT',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  ROLE_MANAGEMENT = 'ROLE_MANAGEMENT',
  INPUT_DATA = 'INPUT_DATA',
  SERVICE_MANAGEMENT = 'SERVICE_MANAGEMENT',
  APPLICATION_MANAGEMENT = 'APPLICATION_MANAGEMENT',
  PRIORITY_CONFIG = 'PRIORITY_CONFIG',
  GROUP_ALERT_CONFIG = 'GROUP_ALERT_CONFIG',
  MAINTENANCE_TIME_CONFIG = 'MAINTENANCE_TIME_CONFIG',
  FILTER_ALERT = 'FILTER_ALERT',
  EMAIL_CONFIG = 'EMAIL_CONFIG',
  DATABASE_CONFIG = 'DATABASE_CONFIG',
  SYSTEM = 'SYSTEM',
  TRIGGER = 'TRIGGER',
  MONITOR_WEB_CONFIG = 'MONITOR_WEB_CONFIG',
  MONITOR_WEB_JOB = 'MONITOR_WEB_JOB',
}

export type SysLogFunction = {
  label: string;
  actions: SysLogActionEnum[];
};

export const SysLogFunctionLabel: EnumKey<SysLogFunctionEnum> = {
  [SysLogFunctionEnum.CUSTOM_OBJECT]: 'Custom Object',
  [SysLogFunctionEnum.USER_MANAGEMENT]: 'User Management',
  [SysLogFunctionEnum.ROLE_MANAGEMENT]: 'Role Management',
  [SysLogFunctionEnum.INPUT_DATA]: 'Input Data',
  [SysLogFunctionEnum.SERVICE_MANAGEMENT]: 'Service Management',
  [SysLogFunctionEnum.APPLICATION_MANAGEMENT]: 'Application Management',
  [SysLogFunctionEnum.PRIORITY_CONFIG]: 'Priority Config',
  [SysLogFunctionEnum.GROUP_ALERT_CONFIG]: 'Alert Group Config',
  [SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG]: 'Maintenance Time Config',
  [SysLogFunctionEnum.FILTER_ALERT]: 'Filter Alert Config',
  [SysLogFunctionEnum.EMAIL_CONFIG]: 'Email Config',
  [SysLogFunctionEnum.DATABASE_CONFIG]: 'Database Config',
  [SysLogFunctionEnum.SYSTEM]: 'System',
  [SysLogFunctionEnum.TRIGGER]: 'Auto Trigger Action Config',
  [SysLogFunctionEnum.MONITOR_WEB_CONFIG]: 'Monitor Web Config',
  [SysLogFunctionEnum.MONITOR_WEB_JOB]: 'Monitor Web Job',
};

export const SysLogActionFunction: EnumKey<SysLogActionEnum, SysLogFunctionEnum> = {
  [SysLogActionEnum.CREATE_CUSTOM_OBJECT]: SysLogFunctionEnum.CUSTOM_OBJECT,
  [SysLogActionEnum.EDIT_CUSTOM_OBJECT]: SysLogFunctionEnum.CUSTOM_OBJECT,
  [SysLogActionEnum.DELETE_CUSTOM_OBJECT]: SysLogFunctionEnum.CUSTOM_OBJECT,

  [SysLogActionEnum.CREATE_USER]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.INACTIVE_USER]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.ACTIVE_USER]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.SET_ADMIN]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.UNSET_ADMIN]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.EDIT_USER]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.DELETE_USER]: SysLogFunctionEnum.USER_MANAGEMENT,
  [SysLogActionEnum.UPDATE_USER_ROLE]: SysLogFunctionEnum.USER_MANAGEMENT,

  [SysLogActionEnum.CREATE_ROLE]: SysLogFunctionEnum.ROLE_MANAGEMENT,
  [SysLogActionEnum.EDIT_ROLE]: SysLogFunctionEnum.ROLE_MANAGEMENT,
  [SysLogActionEnum.INACTIVE_ROLE]: SysLogFunctionEnum.ROLE_MANAGEMENT,
  [SysLogActionEnum.ACTIVE_ROLE]: SysLogFunctionEnum.ROLE_MANAGEMENT,
  [SysLogActionEnum.DELETE_ROLE]: SysLogFunctionEnum.ROLE_MANAGEMENT,

  [SysLogActionEnum.CREATE_WEBHOOK_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.EDIT_WEBHOOK_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.DELETE_WEBHOOK_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.CREATE_DATABASE_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.EDIT_DATABASE_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.DELETE_DATABASE_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.ACTIVE_DATABASE_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.INACTIVE_DATABASE_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.CREATE_EMAIL_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.EDIT_EMAIL_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.DELETE_EMAIL_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.ACTIVE_EMAIL_COLLECT]: SysLogFunctionEnum.INPUT_DATA,
  [SysLogActionEnum.INACTIVE_EMAIL_COLLECT]: SysLogFunctionEnum.INPUT_DATA,

  [SysLogActionEnum.CREATE_SERVICE]: SysLogFunctionEnum.SERVICE_MANAGEMENT,
  [SysLogActionEnum.DELETE_SERVICE]: SysLogFunctionEnum.SERVICE_MANAGEMENT,
  [SysLogActionEnum.EDIT_SERVICE]: SysLogFunctionEnum.SERVICE_MANAGEMENT,

  [SysLogActionEnum.CREATE_APPLICATION]: SysLogFunctionEnum.APPLICATION_MANAGEMENT,
  [SysLogActionEnum.DELETE_APPLICATION]: SysLogFunctionEnum.APPLICATION_MANAGEMENT,
  [SysLogActionEnum.EDIT_APPLICATION]: SysLogFunctionEnum.APPLICATION_MANAGEMENT,

  [SysLogActionEnum.CREATE_PRIORITY_CONFIG]: SysLogFunctionEnum.PRIORITY_CONFIG,
  [SysLogActionEnum.EDIT_PRIORITY_CONFIG]: SysLogFunctionEnum.PRIORITY_CONFIG,
  [SysLogActionEnum.DELETE_PRIORITY_CONFIG]: SysLogFunctionEnum.PRIORITY_CONFIG,
  [SysLogActionEnum.CHANGE_ORDER_OF_PRIORITY_CONFIG]: SysLogFunctionEnum.PRIORITY_CONFIG,

  [SysLogActionEnum.CREATE_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,
  [SysLogActionEnum.EDIT_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,
  [SysLogActionEnum.DELETE_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,
  [SysLogActionEnum.CHANGE_ORDER_OF_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,
  [SysLogActionEnum.ACTIVE_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,
  [SysLogActionEnum.INACTIVE_ALERT_GROUP_CONFIG]: SysLogFunctionEnum.GROUP_ALERT_CONFIG,

  [SysLogActionEnum.CREATE_MAINTENANCE_TIME_CONFIG]: SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG,
  [SysLogActionEnum.EDIT_MAINTENANCE_TIME_CONFIG]: SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG,
  [SysLogActionEnum.DELETE_MAINTENANCE_TIME_CONFIG]: SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG,
  [SysLogActionEnum.ACTIVE_MAINTENANCE_TIME_CONFIG]: SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG,
  [SysLogActionEnum.INACTIVE_MAINTENANCE_TIME_CONFIG]: SysLogFunctionEnum.MAINTENANCE_TIME_CONFIG,

  [SysLogActionEnum.CREATE_FILTER_CONFIG]: SysLogFunctionEnum.FILTER_ALERT,
  [SysLogActionEnum.EDIT_FILTER_CONFIG]: SysLogFunctionEnum.FILTER_ALERT,
  [SysLogActionEnum.DELETE_FILTER_CONFIG]: SysLogFunctionEnum.FILTER_ALERT,
  [SysLogActionEnum.ACTIVE_FILTER_CONFIG]: SysLogFunctionEnum.FILTER_ALERT,
  [SysLogActionEnum.INACTIVE_FILTER_CONFIG]: SysLogFunctionEnum.FILTER_ALERT,

  [SysLogActionEnum.CREATE_PARTNER]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.EDIT_PARTNER]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.DELETE_PARTNER]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.CREATE_TEMPLATE]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.DELETE_TEMPLATE]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.EDIT_TEMPLATE]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.CREATE_EMAIL_CONNECTION]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.EDIT_EMAIL_CONNECTION]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.ACTIVE_EMAIL_CONNECTION]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.INACTIVE_EMAIL_CONNECTION]: SysLogFunctionEnum.EMAIL_CONFIG,
  [SysLogActionEnum.DELETE_EMAIL_CONNECTION]: SysLogFunctionEnum.EMAIL_CONFIG,

  [SysLogActionEnum.CREATE_DATABASE_CONNECT]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.EDIT_DATABASE_CONNECT]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.ACTIVE_DATABASE_CONNECT]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.INACTIVE_DATABASE_CONNECT]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.CREATE_GROUP_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.EDIT_GROUP_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.DELETE_GROUP_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.CREATE_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.EDIT_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.DELETE_QUERY]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.CREATE_DATABASE_THRESHOLD]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.EDIT_DATABASE_THRESHOLD]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.DELETE_DATABASE_THRESHOLD]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.ACTIVE_DATABASE_THRESHOLD]: SysLogFunctionEnum.DATABASE_CONFIG,
  [SysLogActionEnum.INACTIVE_DATABASE_THRESHOLD]: SysLogFunctionEnum.DATABASE_CONFIG,

  [SysLogActionEnum.DENY_ALERT_BY_WRONG_INFORMATION]: SysLogFunctionEnum.SYSTEM,
  [SysLogActionEnum.DENY_ALERT_BY_FILTER_ALERT]: SysLogFunctionEnum.SYSTEM,
  [SysLogActionEnum.MODIFY_ALERT]: SysLogFunctionEnum.SYSTEM,
  [SysLogActionEnum.SEND_TELEGRAM_FAILED]: SysLogFunctionEnum.SYSTEM,

  [SysLogActionEnum.CREATE_MONITOR_WEB_CONFIG]: SysLogFunctionEnum.MONITOR_WEB_CONFIG,
  [SysLogActionEnum.EDIT_MONITOR_WEB_CONFIG]: SysLogFunctionEnum.MONITOR_WEB_CONFIG,
  [SysLogActionEnum.DELETE_MONITOR_WEB_CONFIG]: SysLogFunctionEnum.MONITOR_WEB_CONFIG,
  [SysLogActionEnum.ACTIVE_MONITOR_WEB_CONFIG]: SysLogFunctionEnum.MONITOR_WEB_CONFIG,
  [SysLogActionEnum.INACTIVE_MONITOR_WEB_CONFIG]: SysLogFunctionEnum.MONITOR_WEB_CONFIG,

  [SysLogActionEnum.SUCCESS_MONITOR_WEB_JOB]: SysLogFunctionEnum.MONITOR_WEB_JOB,
  [SysLogActionEnum.LAST_ERROR_MONITOR_WEB_JOB]: SysLogFunctionEnum.MONITOR_WEB_JOB,
  [SysLogActionEnum.ATTEMPT_ERROR_MONITOR_WEB_JOB]: SysLogFunctionEnum.MONITOR_WEB_JOB,
  [SysLogActionEnum.FIRST_ERROR_MONITOR_WEB_JOB]: SysLogFunctionEnum.MONITOR_WEB_JOB,

  [SysLogActionEnum.DATABASE_THRESHOLD_ERROR]: SysLogFunctionEnum.SYSTEM,
  [SysLogActionEnum.DATABASE_THRESHOLD_FAILED]: SysLogFunctionEnum.SYSTEM,

  [SysLogActionEnum.DENY_TRIGGER]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.ERROR_TRIGGER]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.CREATE_AUTO_TRIGGER_EXECUTION]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.EDIT_AUTO_TRIGGER_EXECUTION]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.INACTIVE_AUTO_TRIGGER_EXECUTION]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.ACTIVE_AUTO_TRIGGER_EXECUTION]: SysLogFunctionEnum.TRIGGER,
  [SysLogActionEnum.DELETE_AUTO_TRIGGER_EXECUTION]: SysLogFunctionEnum.TRIGGER,
};
