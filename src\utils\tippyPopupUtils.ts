import tippy, { GetReferenceClientRect, Instance, Props } from 'tippy.js';
import { ReactRenderer } from '@tiptap/react';

/**
 * Common tippy popup configuration
 */
export const getBaseTippyConfig = (clientRect: GetReferenceClientRect, content: HTMLElement) => ({
  getReferenceClientRect: clientRect,
  appendTo: () => document.body,
  content,
  showOnCreate: true,
  interactive: true,
  trigger: 'manual',
  placement: 'bottom-start' as const,
});

/**
 * Create and manage tippy popup for mention suggestions
 */
export function createMentionPopup<T, P>(
  MentionComponent: any,
  props: P,
  editor: any
): {
  component: ReactRenderer<T, P>;
  popup: Instance<Props>[];
  destroy: () => void;
  update: (newProps: P) => void;
} {
  const component = new ReactRenderer(MentionComponent, {
    props,
    editor,
  });

  let popup: Instance<Props>[] = [];

  const create = (clientRect: GetReferenceClientRect) => {
    popup = tippy('body', getBaseTippyConfig(clientRect, component.element));
  };

  const update = (newProps: P) => {
    component.updateProps(newProps);
  };

  const updatePosition = (clientRect: GetReferenceClientRect) => {
    if (popup.length > 0) {
      popup[0].setProps({
        getReferenceClientRect: clientRect,
      });
    }
  };

  const destroy = () => {
    if (popup.length > 0) {
      popup[0].destroy();
    }
    component.destroy();
  };

  const hide = () => {
    if (popup.length > 0) {
      popup[0].hide();
    }
  };

  return {
    component,
    popup,
    destroy,
    update,
    create,
    updatePosition,
    hide,
  } as any;
}

/**
 * Common suggestion render factory
 */
export function createSuggestionRender<T, P>(MentionComponent: any) {
  return () => {
    let popupManager: ReturnType<typeof createMentionPopup<T, P>>;

    return {
      onStart({ editor, clientRect, ...restProps }: any) {
        popupManager = createMentionPopup<T, P>(MentionComponent, { editor, clientRect, ...restProps }, editor);
        
        if (clientRect) {
          popupManager.create(clientRect as GetReferenceClientRect);
        }
      },

      onUpdate({ clientRect, ...restProps }: any) {
        if (popupManager) {
          popupManager.update({ clientRect, ...restProps });
          
          if (clientRect) {
            popupManager.updatePosition(clientRect as GetReferenceClientRect);
          }
        }
      },

      onKeyDown({ event, ...restProps }: any) {
        if (event.key === 'Escape') {
          if (popupManager) {
            popupManager.hide();
          }
          return true;
        }

        return popupManager?.component?.ref?.onKeyDown({ event, ...restProps }) || false;
      },

      onExit() {
        if (popupManager) {
          popupManager.destroy();
        }
      },
    };
  };
}

/**
 * Common suggestion configuration factory
 */
export function createSuggestionConfig<T, P>(
  char: string,
  MentionComponent: any,
  HTMLAttributes?: Record<string, any>
) {
  return {
    char,
    HTMLAttributes: HTMLAttributes || {},
    render: createSuggestionRender<T, P>(MentionComponent),
  };
}
