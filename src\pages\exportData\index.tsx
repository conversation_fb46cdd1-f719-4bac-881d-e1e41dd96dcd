import React, { useState, useMemo, useRef, useCallback } from 'react';
import equal from 'fast-deep-equal';
import {
  ColumnType,
  KanbanIconButton,
  KanbanLoading,
  KanbanTableProps,
  KanbanTableSelectHandleMethods,
  KanbanText,
  KanbanTooltip,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import Table from '@components/table';
import { DEFAULT_DEBOUNCE_TIME } from '@components/ComboboxLoadMore';
import { SortType } from '@common/constants/SortType';
import { DEFAULT_PAGINATION_REQUEST } from '@common/constants/PaginationRequestConstant';
import { SearchPaginationRequest } from '@api/Type';
import { ExportData } from '@core/schema/ExportData';
import { ExportDataApi } from '@api/ExportDataApi';
import useMutate from '@core/hooks/useMutate';
import { ExportFileStatusEnum } from '@common/constants/ExportFileTypeConstants';
import { saveAs } from 'file-saver';
import { IconDownload, IconTrash } from '@tabler/icons-react';
import { DATE_FORMAT } from '@common/constants/DateConstants';
import { dateToString } from '@common/utils/DateUtils';
import { IconX } from '@tabler/icons-react';
import { IconCheck } from '@tabler/icons-react';
import DeleteModal from './DeleteModal';
import { useDisclosure } from '@mantine/hooks';
const STATUS_CONFIG = {
  [ExportFileStatusEnum.DONE]: { icon: <IconCheck color='green' />, label: 'Done' },
  [ExportFileStatusEnum.DELETED]: { icon: <IconTrash color='gray' />, label: 'Deleted' },
  [ExportFileStatusEnum.FAILED]: { icon: <IconX color='red' />, label: 'Failed' },
  [ExportFileStatusEnum.IN_PROCESS]: { icon: <KanbanLoading type='oval' color='blue' size={18} />, label: 'In Process' },
};
export const ManagementExportDataPage = () => {
  const [tableAffected, setTableAffected] = useState<SearchPaginationRequest>(DEFAULT_PAGINATION_REQUEST);
  const [rowSelected, setRowSelected] = useState<ExportData | undefined>(undefined);
  const tableRef = useRef<KanbanTableSelectHandleMethods>(null);
  const [openedModalDel, { close: closePopupDel, open: openModalDel }] = useDisclosure(false);

  const { data: listExportData, refetch: refetchList } = useFetch(ExportDataApi.findAll(tableAffected), {
    showLoading: false,
    placeholderData: (prev) => prev,
  });
  const { isPending: isPendingDownloadFile, mutate: downloadFile } = useMutate(ExportDataApi.download, {
    successNotification: { message: 'Export file successfully' },
    onSuccess: (response) => {
      if (response instanceof Blob) {
        saveAs(response, `${rowSelected?.fileName}.${rowSelected?.extension.toLowerCase()}`);
      }
    },
    showLoading: false,
  });
  const handleUpdateTablePagination = useCallback(
    (data: TableAffactedSafeType<ExportData>) => {
      setTableAffected((state) => ({
        ...state,
        page: data.page - 1,
        size: data.rowsPerPage,
        sortBy: data.sortedBy ? data.sortedBy : DEFAULT_PAGINATION_REQUEST.sortBy,
        sortOrder: data.isReverse ? SortType.ASC : SortType.DESC,
        search: data.search,
      }));
    },
    [setTableAffected],
  );
  const columns: ColumnType<ExportData>[] = useMemo(
    () => [
      { title: 'Name', name: 'fileName', width: '30%' },
      {
        title: 'Status',
        name: 'status',
        width: '10%',
        customRender: (data) => {
          const { icon, label } = STATUS_CONFIG[data as ExportFileStatusEnum] || {};
          return (
            <KanbanText>
              <Flex align={'center'} gap='xs'>
                {icon} {label || data}
              </Flex>
            </KanbanText>
          );
        },
      },
      { title: 'Format', name: 'extension', width: '10%' },
      {
        title: 'Created at',
        name: 'createdDate',
        width: '15%',
        customRender: (data) => dateToString(data, DATE_FORMAT.FORMAT_DD_MM_YYYY_HH_MM_A),
      },
      { title: 'Exported by', name: 'exportedBy', width: '20%' },
    ],
    [],
  );

  const tableViewListRolesProps: KanbanTableProps<ExportData> = useMemo(() => {
    return {
      columns: columns,
      data: listExportData?.data?.content || [],
      showNumericalOrderColumn: true,
      pagination: {
        enable: true,
      },
      sortable: {
        enable: true,
      },
      searchable: {
        enable: true,
        debounceTime: DEFAULT_DEBOUNCE_TIME,
      },
      serverside: {
        totalRows: listExportData?.data?.totalElements ?? 0,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            handleUpdateTablePagination(dataSet);
          }
        },
      },
      showTopBar: true,
      actions: {
        customAction: (data) => {
          return (
            <>
              <KanbanTooltip label='Download'>
                <KanbanIconButton
                  loading={(rowSelected?.id || 0) === data.id && isPendingDownloadFile}
                  variant='transparent'
                  size={'sm'}
                  disabled={data.status !== ExportFileStatusEnum.DONE}
                  onClick={() => {
                    setRowSelected(data);
                    downloadFile(data.id || '');
                  }}>
                  <IconDownload />
                </KanbanIconButton>
              </KanbanTooltip>
              {data.status === ExportFileStatusEnum.DONE && (
                <KanbanTooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      openModalDel();
                      setRowSelected(data);
                    }}>
                    <IconTrash color='red' />
                  </KanbanIconButton>
                </KanbanTooltip>
              )}
            </>
          );
        },
      },
    };
  }, [
    columns,
    downloadFile,
    handleUpdateTablePagination,
    isPendingDownloadFile,
    listExportData?.data?.content,
    listExportData?.data?.totalElements,
    openModalDel,
    rowSelected?.id,
    tableAffected,
  ]);
  return (
    <Box flex={1} p='sm' bg='white'>
      <HeaderTitleComponent title='Management export data' />
      <Table ref={tableRef} {...tableViewListRolesProps} />
      <DeleteModal
        exportDataName={rowSelected?.fileName}
        opened={openedModalDel}
        onClose={closePopupDel}
        exportDataId={rowSelected?.id}
        refetchList={refetchList}
      />
    </Box>
  );
};
export default ManagementExportDataPage;
