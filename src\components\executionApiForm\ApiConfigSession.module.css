.collapsibleSection {
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-md);
  background-color: var(--mantine-color-white);
  margin-bottom: var(--mantine-spacing-md);
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--mantine-spacing-md) var(--mantine-spacing-lg);
  background-color: var(--mantine-color-gray-0);
  border-bottom: 1px solid var(--mantine-color-gray-3);
  cursor: pointer;
  user-select: none;
  border-radius: var(--mantine-radius-md) var(--mantine-radius-md) 0 0;
}

.sectionHeader:hover {
  background-color: var(--mantine-color-gray-1);
}

.sectionTitle {
  font-size: var(--mantine-font-size-lg);
  font-weight: 600;
  color: var(--mantine-color-dark-8);
  margin: 0;
}

.chevronIcon {
  transition: transform 0.2s ease;
  color: var(--mantine-color-gray-6);
}

.chevronIcon.expanded {
  transform: rotate(180deg);
}

.sectionContent {
  padding: var(--mantine-spacing-lg);
}

.apiConfigContainer {
  background-color: var(--mantine-color-white);
  border: none;
  border-radius: 0;
  padding: 0;
}

.methodUrlRow {
  height: 100%;
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: 0;
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  background: var(--mantine-color-white);
  align-items: center;
  overflow: hidden;
  align-items: stretch; 
}

.methodSelect {
  height: 100%;
  width: 100%;
  font-size: var(--mantine-font-size-sm);
  font-weight: 600;
  color: var(--mantine-color-orange-6);
  background: var(--mantine-color-gray-0);
  appearance: none;
  padding: 0 var(--mantine-spacing-sm);
  box-sizing: border-box;
  border: none;                   
  outline: none;      
  display: flex;
  align-items: center;
}
.methodSelect option {
  color: var(--mantine-color-dark-8);
}

/* Table Styles */
.tableContainer {
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  background: var(--mantine-color-white);
  overflow: hidden;
}

.tableWrap {
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  background: var(--mantine-color-white);
  overflow: hidden;
}

.tableHeader {
  display: grid;
  grid-template-columns: 40px 1fr 1fr 1fr 40px;
  gap: 0;
  background-color: var(--mantine-color-gray-0);
  border-bottom: 1px solid var(--mantine-color-gray-3);
  font-weight: 600;
  font-size: var(--mantine-font-size-sm);
  color: var(--mantine-color-gray-7);
}

.tableHeader > * {
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  border-right: 1px solid var(--mantine-color-gray-3);
  display: flex;
  align-items: center;
}

.tableHeader > *:last-child {
  border-right: none;
}

.tableRow {
  display: grid;
  grid-template-columns: 40px 1fr 1fr 1fr 40px;
  gap: 0;
  align-items: center;
  border-bottom: 1px solid var(--mantine-color-gray-1);
}

.tableRow:last-child {
  border-bottom: none;
}

.tableRow > * {
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  border-right: 1px solid var(--mantine-color-gray-1);
}

.tableRow > *:last-child {
  border-right: none;
}

.rowCheckbox {
  width: 16px;
  height: 16px;
  accent-color: var(--mantine-color-blue-6);
  cursor: pointer;
}

.tableCell {
  width: 100%;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  font-size: var(--mantine-font-size-sm);
  font-family: inherit;
  color: var(--mantine-color-gray-7);
  background-color: var(--mantine-color-white);
  transition: border-color 0.2s ease;
}

.tableCell:disabled {
  background-color: var(--mantine-color-gray-2);
  color: var(--mantine-color-gray-5);
}

.actionCell {
  padding: var(--mantine-spacing-xs);
  border: 1px solid var(--mantine-color-gray-3);
  background-color: var(--mantine-color-white);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  width: 50px;
}

.checkboxContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.actionContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.deleteIcon {
  color: black;
  cursor: pointer;
  transition: color 0.2s ease;
}

.deleteIcon:hover {
  color: var(--mantine-color-red-6);
}

.disabledIndicator {
  width: 16px;
  height: 16px;
  background-color: var(--mantine-color-gray-3);
  border-radius: 2px;
}

/* Tabs Styles */
.tabsRoot {
  margin-top: var(--mantine-spacing-lg);
}

.tabsList {
  border-bottom: 2px solid var(--mantine-color-gray-3);
  padding-bottom: 0;
  margin-bottom: var(--mantine-spacing-md);
  background: none;
}

.tabsList button {
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-gray-6);
  border: none;
  background: none;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tabsList button:hover {
  color: var(--mantine-color-orange-6);
  background-color: var(--mantine-color-gray-0);
}

.tabsList button[data-active="true"] {
  color: var(--mantine-color-orange-6);
  border-bottom-color: var(--mantine-color-blue-6);
  background-color: var(--mantine-color-white);
}

.bodyContainer {
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-sm);
}

.authInlineContainer {
  display: grid;
  grid-template-columns: 200px 1fr;
  align-items: flex-start;
  gap: var(--mantine-spacing-sm);
}
.authTypeInlineSection {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.authFieldInlineSection {
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-xs);
}
.authFieldInlineContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.authPasswordRow {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: var(--mantine-spacing-sm);
}

/* Body Section */
.bodyContainer {
  padding: var(--mantine-spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--mantine-spacing-sm);
}

.bodyRadioGroup {
  display: flex;
  gap: var(--mantine-spacing-lg);
  margin-bottom: var(--mantine-spacing-md);
  font-size: var(--mantine-font-size-sm);
}

.bodyTypeContainer {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-sm);
}

.bodyTypeLabel {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-xs);
  cursor: pointer;
  font-weight: 500;
  color: var(--mantine-color-gray-7);
}

.bodyTypeLabel input[type="radio"] {
  accent-color: var(--mantine-color-blue-6);
}

/* Raw Body Controls */
.rawControls {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-md);
  margin-bottom: var(--mantine-spacing-sm);
  padding: var(--mantine-spacing-sm) 0;
}

.rawHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--mantine-spacing-sm);
  padding: var(--mantine-spacing-sm) 0;
}

.rawLabel {
  color: var(--mantine-color-gray-7);
}

.contentTypeSelector {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-xs);
}

.contentTypeSelect {
  width: 150px;
  border: none !important;
  background: transparent !important;
  font-weight: 500;
  color: var(--mantine-color-blue-6);
}

.contentTypeSelect .mantine-Select-input {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
  font-weight: 500;
  color: var(--mantine-color-blue-6);
}

.contentTypeSelect .mantine-Select-input:focus {
  border: none !important;
  box-shadow: none !important;
}

.beautifyBtn {
  margin-right: 8px;
  font-size: 15px;
  color: var(--mantine-color-black-3);
  border: none;
  background: none;
  padding: 0;
}

.variableDropdown {
  position: fixed;
  z-index: 9999;
  background:  var(--mantine-color-white-0);
  border: 1px solid  var(--mantine-color-white-0);
  border-radius: 4px;
  width: 250px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  font-size: 13px;
}

.variableSearchInput {
  width: 100%;
  padding: 6px 8px;
  border: none;
  border-bottom: 1px solid  var(--mantine-color-white-0);
  font-size: 13px;
  box-sizing: border-box;
  outline: none;
}

.variableItem {
  padding: 8px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.variableItem:hover {
  background:  var(--mantine-color-white-0);
}

.variableKey {
  font-weight: 500;
  color: var(--mantine-color-blue-6);
}

.variableValue {
  color: var(--mantine-color-gray-7);
  font-size: 12px;
}

.switchGroup {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

/* Variable highlighting styles */
.paramInputContainer {
  position: relative;
  display: inline-block;
  width: 100%;
}

.paramInputContainer input {
  background: transparent;
  position: relative;
  z-index: 2;
}

.paramInputContainer .highlightOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  padding: inherit;
  border: transparent;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: transparent;
  white-space: nowrap;
  overflow: hidden;
}

.groupConfigWrapper {
  position: relative;
}
.groupConfigHeader {
  position: sticky;
  top: 0;
  background-color:  var(--mantine-color-white-0);
  z-index: 301;
}
/* Form Section Styles */
.session {
  border: 1px solid  var(--mantine-color-white-0);
  border-radius: 8px;
  background-color:  var(--mantine-color-white-0);
}

/* For non-collapsible sections */
.session:not(.collapsibleSection) {
  padding: 20px;
}

/* Settings Section */
.settingsGroup {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--mantine-spacing-sm) 0;
  border-bottom: 1px solid var(--mantine-color-gray-1);
}

.settingsGroup:last-child {
  border-bottom: none;
}

.settingsLabel {
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-gray-7);
}

.highlight {
  color: var(--mantine-color-blue-6);
  font-weight: bold;
}

.myEditor {
  margin-top: var(--mantine-spacing-lg);
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  background: var(--mantine-color-white);
  max-height: 600px;
  overflow-y: auto;
  padding: 0;
}

.ProseMirror {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}
.rawTextarea {
  min-height: 400px;
}
.settingsNote {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-gray-6);
  margin-top: 2px;
  line-height: 1.4;
}

.noAuthContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--mantine-spacing-xl) var(--mantine-spacing-lg);
  text-align: center;
  min-height: 80px;
}

.noAuthTitle {
  color: var(--mantine-color-dark-8);
  margin-bottom: var(--mantine-spacing-xs);
}

.noAuthDescription {
  color: var(--mantine-color-gray-6);
  font-size: var(--mantine-font-size-sm);
  line-height: 1.4;
}

.rawControls {
  margin-bottom: 0;
}

.rawControlsInline {
  margin-left: var(--mantine-spacing-md);
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-sm);
  transform: translateY(var(--mantine-spacing-xs));
}

.rawLabel {
  min-width: fit-content;
}

.contentTypeSelectInline {
  border: 1px solid var(--mantine-color-blue-6) !important;
  background: var(--mantine-color-blue-0) !important;
  border-radius: var(--mantine-radius-sm) !important;
  font-weight: 500;
  color: var(--mantine-color-blue-6) !important;
  min-width: 100px !important;
  max-width: 120px !important;
  width: auto !important;
  height: 32px !important;
}

.contentTypeSelectInline .mantine-Select-input {
  border: none !important;
  background: transparent !important;
  padding: 4px 8px !important;
  font-weight: 500;
  color: var(--mantine-color-blue-6) !important;
  cursor: pointer;
  height: 32px !important;
  min-height: 32px !important;
  font-size: var(--mantine-font-size-sm) !important;
  line-height: 1.4 !important;
}

.contentTypeSelectInline .mantine-Select-input:focus {
  border: none !important;
  box-shadow: 0 0 0 2px var(--mantine-color-blue-2) !important;
}

.contentTypeSelectInline .mantine-Select-rightSection {
  color: var(--mantine-color-blue-6) !important;
}

.beautifyBtn {
  font-weight: 400;
  font-size: var(--mantine-font-size-sm);
  height: 32px;
  padding: 4px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.inputBox {
  width: 100%;
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  font-size: var(--mantine-font-size-sm);
  background-color: var(--mantine-color-white-3);
  min-height: 40px;
  max-height: 40px;
  overflow-x: hidden;
  overflow-y: hidden;
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  transition: max-height 0.2s ease;
  word-break: break-word;

  &.focused {
    max-height: 200px;
    overflow-y: auto;
    border-color: var(--mantine-color-gray-4);
  }

  &.isUrl {
    border: none;
    border-radius: 0;
    padding: var(--mantine-spacing-sm) var(--mantine-spacing-sm);
  }
}

.editorContent {
  width: 100%;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  word-break: break-all;

  &.focused {
    white-space: pre-wrap;
    word-break: break-word;
    -webkit-line-clamp: unset;
    line-clamp: unset;
    overflow: auto;
    display: block;
  }
}
.executeSection {
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-xs);
  background-color: var(--mantine-color-gray-0);
  padding: var(--mantine-spacing-sm);
}