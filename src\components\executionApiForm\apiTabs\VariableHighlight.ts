import { Extension } from '@tiptap/core';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { Decoration, DecorationSet } from '@tiptap/pm/view';

export interface VariableHighlightOptions {
  variableList: string[];
  className: string;
}

export const VariableHighlight = Extension.create<VariableHighlightOptions>({
  name: 'variableHighlight',

  addOptions() {
    return {
      variableList: [],
      className: 'variable-highlight',
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('variableHighlight'),
        props: {
          decorations: ({ doc }) => {
            const decorations: Decoration[] = [];
            const text = doc.textBetween(0, doc.content.size, '\n\n');

            this.options.variableList.forEach((param) => {
              const escaped = param.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
              const regex = new RegExp(escaped, 'g');

              let match;
              while ((match = regex.exec(text)) !== null) {
                const from = match.index + 1;
                const to = from + match[0].length;

                decorations.push(Decoration.inline(from, to, { class: this.options.className }));
              }
            });

            return DecorationSet.create(doc, decorations);
          },
        },
      }),
    ];
  },
});
