import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { ActionIcon, Box, Collapse, Flex, Stack, Title } from '@mantine/core';
import { Execution } from '@core/schema/Execution';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import { KanbanInput, KanbanButton } from 'kanban-design-system';
import { IconChevronDown, IconGlobe } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ExecuteScriptModel, ExecuteScriptModelSchema } from '@models/ExecuteScriptModel';
import { ExecutionApi } from '@api/ExecutionApi';
import useMutate from '@core/hooks/useMutate';
import ApiResult from './ApiResult';
import classes from '@components/executionApiForm/ApiConfigSession.module.css';
import { AxiosError } from 'axios';
import { NotificationError, NotificationWarning } from '@common/utils/NotificationUtils';
import { EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE, EXECUTION_UPDATED_ERROR_CODE } from '../Constants';
import { defaultErrorNotification } from '@core/hooks/Utils';
import ExecutionApiForm from '@components/executionApiForm/ExecutionApiForm';
import { ApiExecuteResponse } from '@core/schema/ExecuteApiResponseSchema';

interface Props {
  execution: Execution;
}

const ApiExecutionEditor = ({ execution }: Props) => {
  const [opened, { close, open, toggle }] = useDisclosure(true);
  const { variables } = execution;
  const [result, setResult] = useState<ApiExecuteResponse | null>(null);

  const mappedExecution = useMemo(() => {
    const v = execution.variables ?? [];
    return {
      ...execution,
      executionId: execution.id,
      variables: v.map((variable) => ({
        id: variable.id,
        name: variable.name,
        value: variable.value || '',
      })),
    };
  }, [execution]);
  const form = useForm<ExecuteScriptModel>({
    defaultValues: mappedExecution,
    resolver: zodResolver(ExecuteScriptModelSchema),
    values: mappedExecution,
  });

  const { formState, getValues, register } = form;

  useEffect(() => {
    open();
    setResult(null);
  }, [execution, open]);

  // API execute mutation
  const { mutate: executeMutate } = useMutate(ExecutionApi.execute, {
    showLoading: false,
    errorNotification: { enable: false },
    onSuccess: (data) => {
      if (data.data && data.data.apiExecutionResponse) {
        setResult(data.data.apiExecutionResponse);
      }
    },
    onError(error) {
      if (error instanceof AxiosError) {
        if (error.code === EXECUTION_UPDATED_ERROR_CODE) {
          NotificationWarning({ title: 'Reload execution information', message: 'Execution info has been changed!' });
          return;
        } else if (error.code === EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE) {
          NotificationWarning({
            title: 'Execution is running longer than expected.',
            message: 'Can view the results upon completion on the execution history',
          });
          return;
        }
      }
      NotificationError(defaultErrorNotification(error));
    },
  });

  const onExecuteClick = useCallback(() => {
    const formValues = getValues();
    executeMutate(formValues);
    close();
  }, [close, executeMutate, getValues]);

  if (execution.type !== ExecutionTypeEnum.API) {
    return null;
  }

  return (
    <Stack gap='xs' p='xs' h='var(--kanban-appshell-maxheight-content)'>
      <Stack gap='xs' style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: 'var(--mantine-radius-xs)' }} p='xs'>
        <Flex align='center' justify='space-between'>
          <Flex align='center' gap='md'>
            <Box w={24} h={24}>
              <IconGlobe size={24} color='var(--mantine-color-orange-5)' />
            </Box>
            <Title order={4} c='primary'>
              {execution.name}
            </Title>
          </Flex>
          <ActionIcon onClick={toggle} variant='outline' size='md'>
            <IconChevronDown size={20} />
          </ActionIcon>
        </Flex>

        {/* Form Execute */}
        <Collapse in={opened}>
          <Stack gap='sm'>
            <Box className='apiConfigContainer'>
              <FormProvider {...form}>
                <ExecutionApiForm isViewMode={true} />
                {/* Variables section */}
                {variables && variables.length > 0 && (
                  <Stack gap='xs'>
                    {variables.map((variable, index) => (
                      <KanbanInput
                        key={variable.id || index}
                        label={variable.name}
                        {...register(`variables.${index}.value`)}
                        maxLength={VARIABLE_MAX_LENGTH}
                      />
                    ))}
                  </Stack>
                )}
              </FormProvider>
            </Box>
          </Stack>
        </Collapse>
      </Stack>

      {/* Execute Button Section */}
      <Box className={classes.executeSection}>
        <Flex justify='space-between' align='center'>
          <KanbanButton fullWidth={false} disabled={!formState.isValid} onClick={onExecuteClick}>
            Execute
          </KanbanButton>
        </Flex>
      </Box>

      {/* Result Section */}
      {result && (
        <Box style={{ flex: 1, overflowY: 'auto' }}>
          <ApiResult result={result} />
        </Box>
      )}
    </Stack>
  );
};

export default ApiExecutionEditor;
