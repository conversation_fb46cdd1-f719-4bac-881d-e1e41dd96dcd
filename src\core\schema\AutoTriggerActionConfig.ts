import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { ServiceSchema } from './Service';
import { ApplicationSchema } from './Application';
import { ExecutionSchema } from './Execution';
import { AutoTriggerActionConfigTypeEnum } from '@pages/admins/autoTriggerActionConfig/Constants';

export const BaseAutoTriggerActionConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  active: z.boolean(),
  executions: z.array(ExecutionSchema),
  ruleGroup: QueryRuleGroupTypeSchema.optional(),
  timeSinceLastTrigger: z.number().optional(),
  triggerType: z.nativeEnum(AutoTriggerActionConfigTypeEnum).optional(),
  cronExpression: z.string().optional(),
});

export type BaseAutoTriggerActionConfig = z.infer<typeof BaseAutoTriggerActionConfigSchema>;
export const AutoTriggerActionConfigSchema = BaseAutoTriggerActionConfigSchema.extend({
  services: z.array(ServiceSchema),
  applications: z.array(ApplicationSchema).optional(),
});

export type AutoTriggerActionConfig = z.infer<typeof AutoTriggerActionConfigSchema>;
