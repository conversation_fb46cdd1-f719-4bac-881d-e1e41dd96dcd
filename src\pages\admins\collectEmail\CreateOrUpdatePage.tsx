import React, { useEffect, useMemo, useState } from 'react';
import { KanbanTitle, KanbanAccordionData, KanbanAccordion, KanbanButton } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Flex, ScrollArea } from '@mantine/core';
import GuardComponent from '@components/GuardComponent';
import { IconUpload } from '@tabler/icons-react';
import { CollectEmailConfigApi } from '@api/CollectEmailConfigApi';
import { CollectEmailConfigModel, CollectEmailConfigModelSchema } from '@models/CollectEmailConfigModel';
import { CollectEmailConfigAction, CollectEmailContentTypeEnum } from '@common/constants/CollectEmailConfigConstant';
import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { isEmpty, isEqual } from 'lodash';
import { IconArrowBack } from '@tabler/icons-react';
import { QueryRuleGroupType, QueryRuleType } from '@core/schema/RuleGroupCondition';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermission } from '@models/AclPermission';
import { CollectEmailConfigTypeEnum } from '@common/constants/CollectEmailConfigTypeConstant';
import BaseInformationSession from './BaseInformationSession';
import ConnectionSession from './ConnectionSession';
import ConditionSession from './ConditionSession';
import OutputSession from './OutputSession';
import TypeSession from './TypeSession';
import { validateQuery } from '@components/queryBuilder';
import { RuleGroupType } from 'react-querybuilder';
interface LabelComponentProps {
  label: string;
}

const TitleComponent: React.FC<LabelComponentProps> = ({ label }) => {
  return <KanbanTitle order={4}>{label}</KanbanTitle>;
};
const BASE_RULE_DEFAULT: QueryRuleType = { field: 'subject', operator: QueryBuilderOperatorEnum.IS, value: '' };
const initialQuery: QueryRuleGroupType = {
  combinator: QueryBuilderCombinatorEnum.AND,
  rules: [BASE_RULE_DEFAULT],
};
const DEFAULT_FORM_COLLECT_EMAIL_CONFIG: CollectEmailConfigModel = {
  name: '',
  description: '',
  applicationId: '',
  serviceId: '',
  emailConfigId: 0,
  priorityConfigId: '',
  type: CollectEmailConfigTypeEnum.EVENT_BASE_ALERT,
  content: '',
  contentValue: '',
  contentType: CollectEmailContentTypeEnum.SAME_BODY,
  intervalTime: 15,
  recipient: '',
  ruleGroup: initialQuery,
  active: true,
};
const CreateOrUpdateCollectEmailConfigPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id } = useParams();
  const idNumber = Number(id) || 0;
  const [currentTab, setCurrentTab] = useState<string>(CollectEmailConfigAction.CREATE);
  const isViewMode = currentTab === CollectEmailConfigAction.VIEW;
  const form = useForm({
    defaultValues: DEFAULT_FORM_COLLECT_EMAIL_CONFIG,
    resolver: zodResolver(CollectEmailConfigModelSchema),
    mode: 'onChange',
  });
  const {
    control,
    formState: { isValid },
    handleSubmit,
    reset,
    watch,
  } = form;
  const contentType = watch('contentType');
  const contentValue = watch('contentValue');
  const ruleGroup = useWatch({ control, name: 'ruleGroup' });
  const isRuleValid = validateQuery(ruleGroup as RuleGroupType);
  const isDisabled = useMemo(() => {
    return (CollectEmailContentTypeEnum.CUSTOM_CONTENT === contentType && isEmpty(contentValue)) || !isValid || !isRuleValid;
  }, [contentType, contentValue, isRuleValid, isValid]);

  //API
  const { data: collectEmailConfigDetail, isFetching: isFetching } = useFetch(CollectEmailConfigApi.findById(idNumber), {
    enabled: idNumber > 0,
  });

  const { mutate: saveMutate } = useMutate(CollectEmailConfigApi.save, {
    successNotification:
      idNumber > 0 && currentTab === CollectEmailConfigAction.UPDATE ? `Update Email Config Successfully` : 'Create Email Config Successfully',
    onSuccess: () => {
      navigate(ROUTE_PATH.EMAIL_COLLECT);
    },
  });

  const onSave = handleSubmit((data) => {
    saveMutate(data);
  });

  useEffect(() => {
    if (collectEmailConfigDetail?.data && !isFetching) {
      reset({
        ...collectEmailConfigDetail.data,
        priorityConfigId: collectEmailConfigDetail.data?.priorityConfigId.toString() || '',
        intervalTime: collectEmailConfigDetail.data?.emailConfigIntervalTime || 15,
        id: isEqual(currentTab, CollectEmailConfigAction.UPDATE) ? idNumber : undefined,
        active: collectEmailConfigDetail.data?.active,
      });
    }
  }, [collectEmailConfigDetail?.data, currentTab, idNumber, isFetching, reset]);

  const accordionItems: KanbanAccordionData[] = useMemo(
    () => [
      {
        key: 'generalInformation',
        content: <BaseInformationSession form={form} isViewMode={isViewMode} />,
        title: <TitleComponent label=' General Information' />,
      },
      {
        key: 'type',
        content: <TypeSession form={form} isViewMode={isViewMode} />,
        title: <TitleComponent label=' Alert type' />,
      },
      {
        key: 'connection',
        content: <ConnectionSession form={form} isViewMode={isViewMode} oldData={collectEmailConfigDetail?.data} />,
        title: <TitleComponent label='Connection' />,
      },
      {
        key: 'condition',
        content: <ConditionSession form={form} isViewMode={isViewMode} />,
        title: <TitleComponent label='Condition' />,
      },
      {
        key: 'output',
        content: <OutputSession form={form} isViewMode={isViewMode} oldData={collectEmailConfigDetail?.data} />,
        title: <TitleComponent label='Output' />,
      },
    ],
    [form, isViewMode, collectEmailConfigDetail?.data],
  );
  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);
  const titleHeader =
    currentTab === CollectEmailConfigAction.VIEW
      ? 'Detail Collect Email Config'
      : currentTab === CollectEmailConfigAction.UPDATE
        ? 'Update Collect Email Config'
        : 'Create Collect Email Config';

  return (
    <>
      <HeaderTitleComponent
        title={titleHeader}
        rightSection={
          <Flex gap={10}>
            <KanbanButton
              leftSection={<IconArrowBack />}
              variant='outline'
              onClick={() => {
                navigate(ROUTE_PATH.EMAIL_COLLECT);
              }}>
              Cancel
            </KanbanButton>
            {!isViewMode && (
              <GuardComponent requirePermissions={[AclPermission.emailCollectCreate, AclPermission.emailCollectEdit]}>
                <KanbanButton disabled={isDisabled} leftSection={<IconUpload />} onClick={onSave}>
                  Save
                </KanbanButton>
              </GuardComponent>
            )}
          </Flex>
        }
      />
      <ScrollArea.Autosize mah={1000}>
        <KanbanAccordion data={accordionItems} defaultValue={['generalInformation', 'type', 'connection', 'condition', 'output']} />
      </ScrollArea.Autosize>
    </>
  );
};

export default CreateOrUpdateCollectEmailConfigPage;
