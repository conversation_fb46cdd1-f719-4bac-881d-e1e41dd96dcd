import React from 'react';
import { Box } from '@mantine/core';
import { EditorContent } from '@tiptap/react';
import { ContentTypeEnum } from '@common/constants/ExecutionConstants';
import { useMentionEditor } from './MentionEditor';
import classes from '../ApiConfigSession.module.css';

interface ParamAwareTextareaProps {
  disabled?: boolean;
  executionParams?: string[];
  field: {
    name: string;
    value: string;
    onChange: (e: { target: { value: string } }) => void;
  };
  handleFocusHighlightClick?: (key: string, fieldName: string, cursorPos: number) => void;
  contentType?: ContentTypeEnum;
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
}

export const ParamAwareTextarea: React.FC<ParamAwareTextareaProps> = ({ disabled, executionParams, field, registerEditor }) => {
  const editor = useMentionEditor({ field, disabled, registerEditor, executionParams, isTextArea: true });

  return (
    <Box pos='relative' flex={1} w='100%'>
      <EditorContent className={classes.myEditor} editor={editor} />
    </Box>
  );
};
