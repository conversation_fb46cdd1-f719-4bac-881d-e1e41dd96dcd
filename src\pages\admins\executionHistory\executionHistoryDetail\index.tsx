import { ExecutionHistoryApi } from '@api/ExecutionHistoryApi';
import { ExecutionTypeEnum, HIDDEN_VARIABLE_PLACEHOLDER } from '@common/constants/ExecutionConstants';
import PythonEditor from '@components/editor/PythonEditor';
import SQLEditor from '@components/editor/SQLEditor';
import EmptyBox from '@components/EmptyBox';
import useFetch from '@core/hooks/useFetch';
import { ActionIcon, Box, Center, Code, Collapse, Flex, Stack, Text, Title } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import ExecutionHistoryStatus from '../ExecutionHistoryStatus';
import { IconChevronDown, IconCode, IconDatabase, IconArrowBack } from '@tabler/icons-react';
import PythonResult from '@components/PythonResult';

const ExecutionHistoryDetailPage = () => {
  const navigate = useNavigate();
  const { executionHistoryId } = useParams();
  const [opened, { toggle }] = useDisclosure(false);
  const { data: executionHistoryData, isLoading } = useFetch(ExecutionHistoryApi.findById(executionHistoryId || '0'), {
    enabled: !!executionHistoryId,
  });
  if (isLoading) {
    return null;
  }

  if (!executionHistoryData?.data) {
    return (
      <Center h='var(--kanban-appshell-maxheight-content)'>
        <Stack>
          <EmptyBox />
          <Text>Can not found execution history!</Text>
        </Stack>
      </Center>
    );
  }

  const {
    data: { error, executionDescription, executionName, executionParams = [], executionScript, executionType, result, status },
  } = executionHistoryData;
  return (
    <Box h='calc(var(--kanban-appshell-maxheight-content) - var(--mantine-spacing-sm) - var(--mantine-spacing-sm) )'>
      <Stack gap='xs' h='100%' style={{ overflow: 'hidden' }}>
        <Stack gap='xs' style={{ border: '1px solid var(--mantine-color-gray-3)' }} p='xs'>
          <Flex align='center' justify='space-between'>
            <Flex align='center' gap='md'>
              <Flex align='center' justify='flex-start' gap='md'>
                <ExecutionHistoryStatus status={status} />
                <Box w={24} h={24}>
                  {ExecutionTypeEnum.SQL === executionType ? (
                    <IconDatabase size={24} color='var(--mantine-color-blue-4)' />
                  ) : (
                    <IconCode size={24} color='var(--mantine-color-green-5)' />
                  )}
                </Box>
                <Title order={4} c='primary'>
                  {executionName}
                  {executionDescription && <Code ml='sm'>{executionDescription}</Code>}
                </Title>
              </Flex>
            </Flex>
            <Flex align='center' gap='md'>
              <KanbanButton leftSection={<IconArrowBack />} variant='outline' onClick={() => navigate('../')}>
                Back
              </KanbanButton>
              <ActionIcon onClick={toggle} variant='outline' size='md'>
                <IconChevronDown size={19} />
              </ActionIcon>
            </Flex>
          </Flex>
          <Collapse in={opened}>
            <Stack gap='xs'>
              {ExecutionTypeEnum.SQL === executionType ? (
                <SQLEditor value={executionScript} readOnly={true} height='300px' />
              ) : (
                <PythonEditor value={executionScript} readOnly={true} height='300px' />
              )}
              {executionParams?.length > 0 &&
                executionParams.map((param, index) => (
                  <KanbanInput key={index} label={param.name} value={param.hidden ? HIDDEN_VARIABLE_PLACEHOLDER : param.value} disabled={true} />
                ))}
            </Stack>
          </Collapse>
        </Stack>

        <Stack maw='100%' flex={1} style={{ overflow: 'hidden' }}>
          <PythonResult error={error} response={result} />
        </Stack>
      </Stack>
    </Box>
  );
};

export default ExecutionHistoryDetailPage;
