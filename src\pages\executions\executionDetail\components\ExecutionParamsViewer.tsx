import React from 'react';
import { Box, Stack } from '@mantine/core';
import { KanbanInput, KanbanTitle } from 'kanban-design-system';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';

interface ExecutionParamsViewerProps {
  executionParams?: string[];
}

const ExecutionParamsViewer: React.FC<ExecutionParamsViewerProps> = ({ executionParams }) => {
  if (!executionParams || executionParams.length === 0) {
    return null;
  }

  return (
    <Box>
      <KanbanTitle size='sm' fw={500} mb='xs'>
        Execution Parameters:
      </KanbanTitle>
      <Stack gap='xs'>
        {executionParams.map((param, index) => (
          <KanbanInput key={index} label={`Parameter ${index + 1}`} value={param} disabled={true} maxLength={VARIABLE_MAX_LENGTH} />
        ))}
      </Stack>
    </Box>
  );
};

export default ExecutionParamsViewer;
