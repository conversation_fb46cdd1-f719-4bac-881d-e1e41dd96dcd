import { VARIABLE_MAX_LENGTH, MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_JSON_PATH_LENGTH } from '@common/constants/ValidationConstant';
import { VariableTypeEnum, DataTypeEnum } from '@common/constants/ExecutionConstants';
import { POSITION_OF_JSON_OBJECT_REGEX } from '@common/constants/RegexConstant';
import { REGEX_POSITION_OF_JSON_ERROR } from '@core/message/MesageConstant';
import { z } from 'zod';

// Schema for Variable Detail (for Dynamic Value type)
// VariableDetailModel with conditional validation - only strict validation for DYNAMIC_VALUE + JSON
const VariableDetailModel = z
  .object({
    id: z.string().optional(),
    name: z.string().optional(), // Made optional, will be validated conditionally
    jsonPath: z.string().optional(), // Made optional, will be validated conditionally
    hidden: z.boolean().optional(),
  })
  .superRefine((_value, _ctx) => {
    // Note: This validation will be triggered from parent VariableModelSchema
    // when we know the context (DYNAMIC_VALUE + JSON)
    // Individual field validation will be handled in the parent schema
  });

export type VariableDetailModel = z.infer<typeof VariableDetailModel>;

export const VariableModelSchema = z
  .object({
    id: z.string().optional(),
    name: z.string().trim().max(MAX_CHARACTER_NAME_LENGTH).optional(),
    description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
    value: z.string().trim().max(VARIABLE_MAX_LENGTH).optional(),
    hidden: z.boolean(),

    // New fields for Dynamic Value
    type: z.nativeEnum(VariableTypeEnum).default(VariableTypeEnum.FIXED_VALUE),
    executionId: z.string().optional(),
    dataType: z.nativeEnum(DataTypeEnum).optional(),
    variableDetails: z.array(VariableDetailModel).optional(),
    enableExpiration: z.boolean().default(false),
    expirationTime: z.number().min(1).optional(),
  })
  .superRefine((value, ctx) => {
    const isCreateMode = !value.id;
    const isFixedValue = value.type === VariableTypeEnum.FIXED_VALUE;
    const isDynamicValue = value.type === VariableTypeEnum.DYNAMIC_VALUE;

    // Validation for Fixed Value type
    if (isFixedValue) {
      // Name is required for Fixed Value
      if (!value.name || value.name.trim().length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['name'],
          message: 'Variable name is required',
        });
      }

      if (isCreateMode && !value.value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['value'],
          message: 'Value can not be empty',
        });
      }
      if (!isCreateMode && !value.hidden && !value.value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['value'],
          message: 'Value can not be empty',
        });
      }
    }

    // Validation for Dynamic Value type
    if (isDynamicValue) {
      if (!value.executionId) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['executionId'],
          message: 'Execution is required for Dynamic Value',
        });
      }

      if (!value.dataType) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['dataType'],
          message: 'Data type is required for Dynamic Value',
        });
      }

      // For JSON data type: variableDetails is required, name is in VariableDetailModel
      if (value.dataType === DataTypeEnum.JSON) {
        if (!value.variableDetails || value.variableDetails.length === 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['variableDetails'],
            message: 'At least one JSON variable is required',
          });
        } else {
          // Validate each VariableDetailModel for DYNAMIC_VALUE + JSON context
          value.variableDetails.forEach((detail, index) => {
            // Name is required for JSON variables
            if (!detail.name || detail.name.trim().length === 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['variableDetails', index, 'name'],
                message: 'Variable name is required',
              });
            } else if (detail.name.trim().length > MAX_CHARACTER_NAME_LENGTH) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['variableDetails', index, 'name'],
                message: `Variable name must be at most ${MAX_CHARACTER_NAME_LENGTH} characters`,
              });
            }

            // Position is required for JSON variables
            if (!detail.jsonPath || detail.jsonPath.trim().length === 0) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['variableDetails', index, 'jsonPath'],
                message: 'JSON position is required',
              });
            } else if (detail.jsonPath.trim().length > MAX_JSON_PATH_LENGTH) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['variableDetails', index, 'jsonPath'],
                message: `JSON position must be at most ${MAX_JSON_PATH_LENGTH} characters`,
              });
            } else if (!POSITION_OF_JSON_OBJECT_REGEX.test(detail.jsonPath.trim())) {
              ctx.addIssue({
                code: z.ZodIssueCode.custom,
                path: ['variableDetails', index, 'jsonPath'],
                message: REGEX_POSITION_OF_JSON_ERROR,
              });
            }
          });
        }
      } else {
        // Name is required for Fixed Value
        if (!value.name || value.name.trim().length === 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['name'],
            message: 'Variable name is required',
          });
        }
      }

      if (value.enableExpiration && !value.expirationTime) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['expirationTime'],
          message: 'Expiration time is required when expiration is enabled',
        });
      }
    }
  });

export type VariableModel = z.infer<typeof VariableModelSchema>;
