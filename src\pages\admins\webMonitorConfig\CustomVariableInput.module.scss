.container {
  width: 100%;
}

.label {
  display: block;
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-text);
  border-bottom: 2px solid transparent;
}

.required {
  color: var(--mantine-color-error);
  margin-left: 2px;
}

.editorWrapper {
  border: 1px solid var(--mantine-color-default-border);
  border-radius: var(--mantine-radius-default);
  min-height: 34px;
  background: var(--mantine-color-body);
  transition: border-color 150ms ease;

  &:focus-within {
    border-color: var(--mantine-color-blue-filled);
    box-shadow: 0 0 0 1px #228be6;
  }

  &.disabledWrapper {
    cursor: not-allowed;
    opacity: .6;
    background-color: var(--input-disabled-bg);
    color: var(--input-disabled-color);
  }
}

.editor {
  padding: 8px 12px;
  min-height: 34px;
  outline: none;
  font-size: var(--mantine-font-size-sm);
  line-height: var(--mantine-line-height);
  color: var(--mantine-color-text);

  &.disabled {
    cursor: not-allowed;
    opacity: .6;
    background-color: var(--input-disabled-bg);
    color: var(--input-disabled-color);
  }

  p {
    margin: 0;
  }

  &:focus {
    outline: none;
  }
}

.mention {
  background-color: var(--mantine-color-blue-light);
  border: 1px solid var(--mantine-color-blue-filled);
  border-radius: var(--mantine-radius-sm);
  margin: 0 2px;
  color: var(--mantine-color-blue-filled);
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  font-size: var(--mantine-font-size-xs);

  &:before {
    content: '@@';
    font-weight: normal;
  }

  @mixin dark {
    background-color: var(--mantine-color-blue-dark);
    border-color: var(--mantine-color-blue-6);
    color: var(--mantine-color-blue-4);
  }
}
