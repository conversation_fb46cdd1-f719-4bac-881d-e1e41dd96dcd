.container {
  width: 100%;
}

.label {
  margin-bottom: 8px;
  display: block;
}

.required {
  color: red;
  margin-left: 2px;
}

.editorWrapper {
  border: 1px solid #ced4da;
  border-radius: 4px;
  min-height: 40px;
  background: white;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: #228be6;
    box-shadow: 0 0 0 1px #228be6;
  }

  &.disabledWrapper {
    background-color: #f8f9fa;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.editor {
  padding: 8px 12px;
  min-height: 24px;
  outline: none;
  font-size: 14px;
  line-height: 1.4;

  &.disabled {
    color: #868e96;
    cursor: not-allowed;
  }

  p {
    margin: 0;
  }

  &:focus {
    outline: none;
  }
}

.mention {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 4px;
  padding: 2px 6px;
  margin: 0 2px;
  color: #1976d2;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  font-size: 13px;

  &:before {
    content: '{{ ';
    font-weight: normal;
  }

  &:after {
    content: ' }}';
    font-weight: normal;
  }
}
