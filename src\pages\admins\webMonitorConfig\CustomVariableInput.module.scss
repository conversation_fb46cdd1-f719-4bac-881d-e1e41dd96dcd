.container {
  width: 100%;
}

.label {
  display: block;
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-text);
  border-bottom: 2px solid transparent;
}

.required {
  color: red;
  margin-left: 2px;
}

.editorWrapper {
  border: 1px solid #ced4da;
  border-radius: 4px;
  min-height: 34px;
  background: white;
  transition: all 0.2s ease;

  &:focus-within {
    border-color: #228be6;
    box-shadow: 0 0 0 1px #228be6;
  }

  &.disabledWrapper {
    background-color: #f8f9fa;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.editor {
  padding: 8px 12px;
  min-height: 34px;
  outline: none;
  font-size: 14px;
  line-height: 1.4;

  &.disabled {
    color: #868e96;
    cursor: not-allowed;
  }

  p {
    margin: 0;
  }

  &:focus {
    outline: none;
  }
}

.mention {
  background-color: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 4px;
  margin: 0 2px;
  color: #1976d2;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  font-size: 13px;
}
