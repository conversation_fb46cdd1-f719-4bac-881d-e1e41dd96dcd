import { use<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Editor } from '@tiptap/react';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Mention from '@tiptap/extension-mention';
import tippy, { GetReferenceClientRect, Instance } from 'tippy.js';
import classes from '../ApiConfigSession.module.css';
import { ParamList, ParamListActions, ParamListProps } from './ParamList';
import { convertTextToMentions, extractTextFromMentionJSON } from '@common/utils/ExecutionApiUtils';
import { useEffect } from 'react';
import CharacterCount from '@tiptap/extension-character-count';
import { EXECUTION_API_PARAM_VALUE_PREFIX } from '@pages/admins/executionConfig/Constants';
import keyboardKey from 'keyboard-key';

export interface MyTiptapEditorRef {
  focus: () => void;
}
export interface UseMentionEditorProps {
  disabled?: boolean;
  field: {
    name: string;
    value: string;
    onChange: (e: { target: { value: string } }) => void;
  };
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
  executionParams?: string[];
  maxLength?: number;
  isTextArea?: boolean;
}

export const mentionPopupState = {
  isSelecting: false,
};

export function useMentionEditor({ disabled, executionParams, field, isTextArea, maxLength, registerEditor }: UseMentionEditorProps) {
  const editor = useEditor({
    extensions: [
      Document,
      Paragraph,
      Text,
      CharacterCount.configure({
        limit: maxLength,
      }),
      Mention.configure({
        HTMLAttributes: { class: classes.highlight },
        renderText: ({ node }) => node.attrs.label ?? node.attrs.id,
        suggestion: {
          char: EXECUTION_API_PARAM_VALUE_PREFIX,
          allowSpaces: false,
          startOfLine: false,
          allowedPrefixes: null,
          items: ({ query }) => {
            const results = (executionParams || [])
              .filter((name) => name.toLowerCase().includes(query.toLowerCase()))
              .map((name) => ({
                id: name,
                label: name,
              }));
            return results;
          },
          render: () => {
            let component: ReactRenderer<ParamListActions, ParamListProps & React.RefAttributes<ParamListActions>> | undefined;
            let popup: Instance | null = null;

            return {
              onStart: (props) => {
                if (popup) {
                  popup.destroy();
                }
                mentionPopupState.isSelecting = true;
                component = new ReactRenderer(ParamList, {
                  props,
                  editor: props.editor,
                });

                if (!props.clientRect) {
                  return;
                }

                const rect: GetReferenceClientRect = () => {
                  const r = props.clientRect?.();
                  return r || new DOMRect();
                };
                const popups = tippy('body', {
                  getReferenceClientRect: rect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  placement: 'bottom-start',
                });
                popup = Array.isArray(popups) ? popups[0] : popups;
              },
              onUpdate: (props) => {
                component?.updateProps(props);
                popup?.setProps({
                  getReferenceClientRect: () => props.clientRect?.() || new DOMRect(),
                });
              },
              onKeyDown: (props) => {
                if (keyboardKey.Escape === keyboardKey.getCode(props.event)) {
                  popup?.hide();
                  return true;
                }
                if (component?.ref) {
                  return (component.ref as ParamListActions).onKeyDown?.(props);
                }
                return false;
              },
              onExit: () => {
                mentionPopupState.isSelecting = false;
                popup?.destroy();
                component?.destroy();
              },
            };
          },
          command: ({ editor, props, range }) => {
            editor
              .chain()
              .focus()
              .deleteRange(range)
              .insertContent({
                type: 'mention',
                attrs: { id: props.id, label: props.label || props.id },
              })
              .run();
          },
        },
      }),
    ],
    content: convertTextToMentions(field.value || ''),
    editable: !disabled,
    onUpdate: ({ editor }) => {
      const plainText = extractTextFromMentionJSON(editor.getJSON());
      if (plainText !== field.value) {
        field.onChange({ target: { value: plainText } });
      }
    },
    editorProps: {
      attributes: {
        class: isTextArea ? classes.rawTextarea : classes.ProseMirror,
      },
    },
  });

  useEffect(() => {
    if (editor && field.name) {
      (editor.options as Editor['options'] & { fieldName?: string }).fieldName = field.name;
    }
  }, [editor, field.name]);

  useEffect(() => {
    if (editor && registerEditor && field.name) {
      registerEditor(field.name, (text, pos) => {
        const label = text.replace(EXECUTION_API_PARAM_VALUE_PREFIX, '');
        const safePos = typeof pos === 'number' && pos >= 0 ? pos : editor.state.selection.from;
        editor
          .chain()
          .focus()
          .insertContentAt(safePos, {
            type: 'mention',
            attrs: { id: label, label },
          })
          .run();
      });
    }
  }, [editor, registerEditor, field.name]);

  useEffect(() => {
    if (!editor) {
      return;
    }
    const currentText = extractTextFromMentionJSON(editor.getJSON());
    if (field.value !== currentText) {
      const cursor = editor.state.selection.from;
      editor.commands.setContent(convertTextToMentions(field.value), false);
      setTimeout(() => editor.commands.setTextSelection(cursor), 0);
    }
  }, [editor, field.value]);

  return editor;
}
