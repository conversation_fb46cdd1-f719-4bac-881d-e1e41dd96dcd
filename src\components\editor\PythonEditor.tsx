import React from 'react';
import { python } from '@codemirror/lang-python';
import { autocompletion, CompletionContext, CompletionResult } from '@codemirror/autocomplete';
import { BaseEditorProps, Variable } from './Type';
import BaseEditor from './BaseEditor';

interface PythonEditorProps extends BaseEditorProps {
  variables?: Variable[];
}

const PythonEditor = ({ variables = [], ...resetProps }: PythonEditorProps) => {
  const envVarCompletion = (context: CompletionContext): CompletionResult | null => {
    const line = context.state.doc.lineAt(context.pos);
    const lineText = line.text.slice(0, context.pos - line.from);

    // Match @@ to suggest environment variables
    const atAtMatch = /@@([^@]*)$/.exec(lineText);
    if (atAtMatch) {
      const prefix = atAtMatch[1] || '';
      const filteredEnvs = prefix ? variables.filter((env) => env.name.toLowerCase().includes(prefix.toLowerCase())) : variables;
      return {
        from: context.pos - prefix.length - 2,
        options: filteredEnvs.map((env) => ({
          label: env.name,
          detail: env.hiddenValue ? '' : env.value,
          apply(view, completion, from, to) {
            const doc = view.state.doc.toString();

            // Prepare the insertion text
            const insertionText = `os.environ["${env.name}"]`;

            // Check if `import os` exists already
            if (!doc.includes('import os')) {
              // Perform a transaction to insert both the import and the completion
              view.dispatch({
                changes: [
                  { from, to, insert: insertionText },
                  { from: 0, insert: 'import os;\n' },
                ],
              });
            } else {
              // Only insert the completion text
              view.dispatch({
                changes: { from, to, insert: insertionText },
              });
            }

            // Focus the editor after insertion
            view.focus();
          },
          type: 'variable',
        })),
        filter: false,
      };
    }

    return null;
  };

  return (
    <BaseEditor
      {...resetProps}
      extensions={[
        python(),
        autocompletion({
          override: [envVarCompletion],
        }),
      ]}
      placeholder='Enter python script, use os.environ["variable name"] to send variable value'
    />
  );
};

export default PythonEditor;
