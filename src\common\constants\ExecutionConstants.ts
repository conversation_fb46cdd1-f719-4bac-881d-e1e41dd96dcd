import { EnumKey } from '@common/utils/Type';
import { ComboboxItem } from '@mantine/core';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';

export enum ExecutionTypeEnum {
  SQL = 'SQL',
  PYTHON = 'PYTHON',
  API = 'API',
}

export enum ExecutionStatusEnum {
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export const HIDDEN_VARIABLE_PLACEHOLDER = '*****';

// Variable Type Enums
export enum VariableTypeEnum {
  FIXED_VALUE = 'FIXED_VALUE',
  DYNAMIC_VALUE = 'DYNAMIC_VALUE',
}

export const VariableTypeLabel: EnumKey<VariableTypeEnum> = {
  [VariableTypeEnum.FIXED_VALUE]: 'Fixed value',
  [VariableTypeEnum.DYNAMIC_VALUE]: 'Dynamic value',
};

// Data Type Enums for Dynamic Variables
export enum DataTypeEnum {
  JSON = 'JSON',
  RAW = 'RAW',
}

export const DataTypeLabel: EnumKey<DataTypeEnum> = {
  [DataTypeEnum.JSON]: 'JSON',
  [DataTypeEnum.RAW]: 'Raw',
};

export const ExecutionTypeLabel: EnumKey<ExecutionTypeEnum> = {
  [ExecutionTypeEnum.PYTHON]: 'Python',
  [ExecutionTypeEnum.SQL]: 'SQL',
  [ExecutionTypeEnum.API]: 'API',
};

export type ExecutionStatusInfo = {
  label: string;
  color: string;
  bgColor: string;
};

export const ExecutionStatusInfo: EnumKey<ExecutionStatusEnum, ExecutionStatusInfo> = {
  [ExecutionStatusEnum.IN_PROGRESS]: {
    label: 'Inprogress',
    color: '--mantine-primary-color-8',
    bgColor: '--mantine-primary-color-2',
  },
  [ExecutionStatusEnum.COMPLETED]: {
    label: 'Completed',
    color: '--mantine-color-green-8',
    bgColor: '--mantine-color-green-2',
  },
  [ExecutionStatusEnum.FAILED]: {
    label: 'Failed',
    color: '--mantine-color-red-8',
    bgColor: '--mantine-color-red-2',
  },
  [ExecutionStatusEnum.CANCELED]: {
    label: 'Canceled',
    color: '--mantine-color-gray-8',
    bgColor: '--mantine-color-gray-2',
  },
};
export enum MethodTypeEnum {
  POST = 'POST',
  GET = 'GET',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
  OPTIONS = 'OPTIONS',
}

export enum HttpVersionEnum {
  HTTP1x = 'HTTP1x',
  HTTP2 = 'HTTP2',
}
export enum AuthTypeEnum {
  NONE = 'NONE',
  BASIC = 'BASIC',
  BEARER = 'TOKEN',
}
export const optionAuthTypes: ComboboxItem[] = [
  {
    label: 'No Auth',
    value: AuthTypeEnum.NONE,
  },
  {
    label: 'Basic Auth',
    value: AuthTypeEnum.BASIC,
  },
  {
    label: 'Bearer Token',
    value: AuthTypeEnum.BEARER,
  },
];
export const AuthTypeLabel: EnumKey<AuthTypeEnum> = {
  [AuthTypeEnum.NONE]: 'No Auth',
  [AuthTypeEnum.BASIC]: 'Basic Auth',
  [AuthTypeEnum.BEARER]: 'Bearer Token',
};

export enum BodyTypeEnum {
  NONE = 'NONE',
  URLENCODED = 'URLENCODED',
  RAW = 'RAW',
}
export const BodyTypeLabel: EnumKey<BodyTypeEnum> = {
  [BodyTypeEnum.RAW]: 'raw',
  [BodyTypeEnum.NONE]: 'none',
  [BodyTypeEnum.URLENCODED]: 'x-www-form-urlencoded',
};
export const METHOD_COLORS: EnumKey<MethodTypeEnum> = {
  GET: 'var(--mantine-color-blue-5)',
  POST: 'var(--mantine-color-yellow-7)',
  PUT: 'var(--mantine-color-teal-5)',
  PATCH: 'var(--mantine-color-orange-6)',
  DELETE: 'var(--mantine-color-red-6)',
  OPTIONS: 'var(--mantine-color-cyan-5)',
};

export const HTTP_VERSIONS: EnumKey<HttpVersionEnum> = {
  HTTP1x: 'HTTP/1.x',
  HTTP2: 'HTTP/2',
};

export const HTTP_VERSION_OPTIONS = Object.entries(HTTP_VERSIONS).map(([key, label]) => ({
  value: key,
  label,
}));

// Content Types for Raw Body
export enum ContentTypeEnum {
  TEXT = 'TEXT',
  JAVASCRIPT = 'JAVASCRIPT',
  JSON = 'JSON',
  HTML = 'HTML',
  XML = 'XML',
}

export const ContentTypeLabel: Record<ContentTypeEnum, string> = {
  [ContentTypeEnum.TEXT]: 'Text',
  [ContentTypeEnum.JAVASCRIPT]: 'JavaScript',
  [ContentTypeEnum.JSON]: 'JSON',
  [ContentTypeEnum.HTML]: 'HTML',
  [ContentTypeEnum.XML]: 'XML',
};

export const CONTENT_TYPE_OPTIONS = Object.entries(ContentTypeLabel).map(([value, label]) => ({
  value,
  label,
}));

export const getContentTypeValue = (contentType?: string): string => {
  const contentTypeMap: Record<string, string> = {
    [ContentTypeEnum.TEXT]: 'text/plain',
    [ContentTypeEnum.JAVASCRIPT]: 'application/javascript',
    [ContentTypeEnum.JSON]: 'application/json',
    [ContentTypeEnum.HTML]: 'text/html',
    [ContentTypeEnum.XML]: 'application/xml',
  };
  if (!contentType) {
    return contentTypeMap[ContentTypeEnum.TEXT];
  }

  return contentTypeMap[contentType] || contentTypeMap[ContentTypeEnum.TEXT];
};

export const getFormUrlencodedContentType = (): string => 'application/x-www-form-urlencoded';

export type FormApiInfoModel = {
  apiInfo?: ExecutionApiInfoModel;
};
