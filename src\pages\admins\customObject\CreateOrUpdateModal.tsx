import React, { useEffect, useMemo } from 'react';
import { KanbanButton, KanbanSelect, KanbanInput, KanbanNumberInput } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { useForm, zodResolver } from '@mantine/form';
import useMutate from '@core/hooks/useMutate';
import { CustomObject } from '@core/schema/CustomObject';
import { CustomObjectApi } from '@api/CustomObjectApi';
import { CustomObjectModel, CustomObjectModelSchema } from '@models/CustomObjectModel';
import { CustomObjectTypeEnum } from '@common/constants/CustomObjectTypeConstant';
import { Grid } from '@mantine/core';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { CUSTOM_OBJECT_INDEX_MAX } from '@common/constants/ValidationConstant';
import Modal from '@components/Modal';
import DependenciesWarningAlert, { DependencyItem } from '@components/DependenciesWarningAlert';

type CreateOrUpdateModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  customObject?: CustomObject;
};

const CHARACTER_NAME_CUSTOM_OBJECT_MAX_LENGTH: number = 100;
const CHARACTER_DESCRIPTION_CUSTOM_OBJECT_MAX_LENGTH: number = 300;
const CHARACTER_KEYWORD_CUSTOM_OBJECT_MAX_LENGTH: number = 20;
const CHARACTER_REGEX_CUSTOM_OBJECT_MAX_LENGTH: number = 250;
const DEFAULT_VALUE_CUSTOM_OBJECT: Partial<CustomObjectModel> = {
  regex: undefined,
  fromIndex: undefined,
  toIndex: undefined,
  fromKeyword: undefined,
  toKeyword: undefined,
};
const DEFAULT_FORM_CUSTOM_OBJECT: CustomObjectModel = {
  name: '',
  description: '',
  type: CustomObjectTypeEnum.REGEX,
  ...DEFAULT_VALUE_CUSTOM_OBJECT,
};

const CreateOrUpdateModal: React.FC<CreateOrUpdateModalProps> = ({ customObject, onClose, opened, refetchList }) => {
  const isUpdateMode = !!customObject;
  const { data: customObjectDetail } = useFetch(CustomObjectApi.findById(customObject?.id || 0), {
    enabled: isUpdateMode && opened,
  });
  const { data: dependencies } = useFetch(CustomObjectApi.findAllDependenciesById(customObject?.id || 0), {
    enabled: isUpdateMode,
  });
  const { mutate: saveMutate } = useMutate(CustomObjectApi.save, {
    successNotification: {
      message: isUpdateMode ? `Update Custom Object Successfully` : 'Create Custom Object Successfully',
    },
    onSuccess: () => {
      setValues(DEFAULT_FORM_CUSTOM_OBJECT);
      refetchList();
      onClose();
    },
  });
  const { getInputProps, isValid, setValues, validate, validateField, values } = useForm({
    initialValues: DEFAULT_FORM_CUSTOM_OBJECT,
    validate: zodResolver(CustomObjectModelSchema),
    validateInputOnChange: true,
  });

  const handleSave = () => {
    if (!validate().hasErrors) {
      saveMutate(CustomObjectModelSchema.parse(values));
    }
  };

  useEffect(() => {
    if (!isUpdateMode) {
      setValues({ ...DEFAULT_FORM_CUSTOM_OBJECT });
      return;
    }
    if (customObjectDetail?.data) {
      setValues({
        id: customObjectDetail?.data?.id || 0,
        name: customObjectDetail?.data?.name || '',
        description: customObjectDetail?.data?.description || '',
        type: customObjectDetail?.data?.type || CustomObjectTypeEnum.REGEX,
        regex: customObjectDetail?.data?.regex || undefined,
        fromIndex: customObjectDetail?.data?.fromIndex || undefined,
        toIndex: customObjectDetail?.data?.toIndex || undefined,
        fromKeyword: customObjectDetail?.data?.fromKeyword || undefined,
        toKeyword: customObjectDetail?.data?.toKeyword || undefined,
      });
    }
  }, [customObjectDetail?.data, opened, setValues, customObject, isUpdateMode]);

  const renderTypeSpecificInput = useMemo(() => {
    switch (values.type) {
      case CustomObjectTypeEnum.REGEX:
        return (
          <KanbanInput
            required
            label='Regex'
            {...getInputProps('regex')}
            value={values.regex}
            maxLength={CHARACTER_REGEX_CUSTOM_OBJECT_MAX_LENGTH}
            description={getMaxLengthMessage(CHARACTER_REGEX_CUSTOM_OBJECT_MAX_LENGTH)}
            disabled={isUpdateMode}
          />
        );
      case CustomObjectTypeEnum.INDEX_TO_INDEX:
        return (
          <Grid>
            <Grid.Col span={6}>
              <KanbanNumberInput
                required
                label='From index'
                {...getInputProps('fromIndex')}
                disabled={isUpdateMode}
                onChange={(...params) => {
                  getInputProps('fromIndex').onChange(...params);
                  validateField('toIndex');
                }}
                max={CUSTOM_OBJECT_INDEX_MAX}
                allowDecimal={false}
                allowNegative={false}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <KanbanNumberInput
                required
                label='To index'
                {...getInputProps('toIndex')}
                disabled={isUpdateMode}
                onChange={(...params) => {
                  getInputProps('toIndex').onChange(...params);
                  validateField('fromIndex');
                }}
                max={CUSTOM_OBJECT_INDEX_MAX}
                allowDecimal={false}
                allowNegative={false}
              />
            </Grid.Col>
          </Grid>
        );
      case CustomObjectTypeEnum.KEYWORD_TO_KEYWORD:
        return (
          <Grid>
            <Grid.Col span={6}>
              <KanbanInput
                required
                label='From keyword'
                maxLength={CHARACTER_KEYWORD_CUSTOM_OBJECT_MAX_LENGTH}
                {...getInputProps('fromKeyword')}
                description={getMaxLengthMessage(CHARACTER_KEYWORD_CUSTOM_OBJECT_MAX_LENGTH)}
                disabled={isUpdateMode}
                onChange={(...params) => {
                  getInputProps('fromKeyword').onChange(...params);
                  validateField('toKeyword');
                }}
              />
            </Grid.Col>
            <Grid.Col span={6}>
              <KanbanInput
                required
                label='To keyword'
                maxLength={CHARACTER_KEYWORD_CUSTOM_OBJECT_MAX_LENGTH}
                {...getInputProps('toKeyword')}
                description={getMaxLengthMessage(CHARACTER_KEYWORD_CUSTOM_OBJECT_MAX_LENGTH)}
                disabled={isUpdateMode}
                onChange={(...params) => {
                  getInputProps('toKeyword').onChange(...params);
                  validateField('fromKeyword');
                }}
              />
            </Grid.Col>
          </Grid>
        );
    }
  }, [values, getInputProps, isUpdateMode, validateField]);
  const dependencyConfig: DependencyItem[] = [
    {
      dependencyEntity: 'collect email configs',
      dependencies: dependencies?.data?.collectEmailConfigs ?? [],
    },
    {
      dependencyEntity: 'alert group configs',
      dependencies: dependencies?.data?.alertGroupConfigs ?? [],
    },
    {
      dependencyEntity: 'maintenance time configs',
      dependencies: dependencies?.data?.maintenanceTimeConfigs ?? [],
    },
    {
      dependencyEntity: 'filter alert configs',
      dependencies: dependencies?.data?.filterAlertConfigs ?? [],
    },
    {
      dependencyEntity: 'modify alert configs',
      dependencies: dependencies?.data?.modifyAlertConfigs ?? [],
    },
  ];

  return (
    <Modal
      size={'xl'}
      opened={opened}
      onClose={() => {
        onClose();
        setValues(DEFAULT_FORM_CUSTOM_OBJECT);
      }}
      title={customObject ? `Update Custom Object ${customObject.name}` : 'Create Custom Object'}
      actions={
        <KanbanButton onClick={handleSave} disabled={!isValid()}>
          Save
        </KanbanButton>
      }>
      <DependenciesWarningAlert mainEntity='Custom object' dependencyConfigs={dependencyConfig} isDeleted={false} />
      <form>
        <KanbanInput
          required
          label='Name'
          description={getMaxLengthMessage(CHARACTER_NAME_CUSTOM_OBJECT_MAX_LENGTH)}
          {...getInputProps('name')}
          maxLength={CHARACTER_NAME_CUSTOM_OBJECT_MAX_LENGTH}
        />
        <KanbanInput
          label='Description'
          description={getMaxLengthMessage(CHARACTER_DESCRIPTION_CUSTOM_OBJECT_MAX_LENGTH)}
          {...getInputProps('description')}
          maxLength={CHARACTER_DESCRIPTION_CUSTOM_OBJECT_MAX_LENGTH}
        />
        <KanbanSelect
          required
          label='Type'
          {...getInputProps('type')}
          data={Object.keys(CustomObjectTypeEnum).sort()}
          onChange={(val) => {
            setValues((prev) => ({
              ...prev,
              type: val as CustomObjectTypeEnum,
              ...DEFAULT_VALUE_CUSTOM_OBJECT,
            }));
          }}
          allowDeselect={false}
          disabled={isUpdateMode}
        />
        {renderTypeSpecificInput}
      </form>
    </Modal>
  );
};

export default CreateOrUpdateModal;
