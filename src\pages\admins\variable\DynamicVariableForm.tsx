import { DataType<PERSON>num, DataTypeLabel, ExecutionTypeEnum, VariableTypeEnum } from '@common/constants/ExecutionConstants';
import { MAX_CHARACTER_NAME_LENGTH, MAX_DESCRIPTION_LENGTH, MAX_JSON_PATH_LENGTH } from '@common/constants/ValidationConstant';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { VariableModel } from '@models/VariableModel';
import { KanbanCheckbox, KanbanInput, KanbanSelect, KanbanNumberInput, KanbanButton } from 'kanban-design-system';
import React, { useMemo, useEffect } from 'react';
import { Controller, UseFormReturn, useWatch, useFieldArray } from 'react-hook-form';
import { Flex, SimpleGrid, Box, Stack } from '@mantine/core';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import useFetch from '@core/hooks/useFetch';
import { ExecutionApi } from '@api/ExecutionApi';

interface Props {
  form: UseFormReturn<VariableModel>;
  readonly: boolean;
}

interface JsonVariablesSectionProps {
  form: UseFormReturn<VariableModel>;
  readonly: boolean;
  fields: any[];
  append: (value: any) => void;
  remove: (index: number) => void;
}

const JsonVariablesSection = ({ append, fields, form, readonly, remove }: JsonVariablesSectionProps) => {
  const { control } = form;
  const dataType = useWatch({ control, name: 'dataType' });
  const isRawType = dataType === DataTypeEnum.RAW;

  return (
    <Stack gap='sm'>
      <Box>
        <strong>{isRawType ? 'Variable' : 'JSON Variables'}</strong>
      </Box>

      {fields.map((field, index) => (
        <SimpleGrid key={field.id} cols={isRawType ? 2 : 3} spacing='sm'>
          <Controller
            name={`variableDetails.${index}.name`}
            control={control}
            render={({ field: nameField }) => (
              <KanbanInput
                label='Name'
                required
                {...nameField}
                maxLength={MAX_CHARACTER_NAME_LENGTH}
                placeholder='Variable name'
                disabled={readonly}
                onBlur={(e) => {
                  const value = e.target.value.replace(/\s/g, '');
                  nameField.onChange(value);
                  nameField.onBlur();
                }}
              />
            )}
          />

          {/* Only show Position field for JSON type */}
          {!isRawType && (
            <Controller
              name={`variableDetails.${index}.jsonPath`}
              control={control}
              render={({ field: positionField }) => (
                <KanbanInput
                  label='Position of json object'
                  required
                  {...positionField}
                  maxLength={MAX_JSON_PATH_LENGTH}
                  placeholder='$.json.object'
                  disabled={readonly}
                />
              )}
            />
          )}

          <Flex align='center' justify='center' gap='xs'>
            <Controller
              name={`variableDetails.${index}.hidden`}
              control={control}
              render={({ field: hiddenField }) => (
                <KanbanCheckbox label='Hidden' checked={hiddenField.value} onChange={hiddenField.onChange} disabled={readonly} />
              )}
            />

            {/* Show delete button for JSON type, disable when only 1 variable remains */}
            {!readonly && !isRawType && (
              <KanbanButton variant='subtle' color='red' size='xs' onClick={() => remove(index)} disabled={fields.length <= 1}>
                <IconTrash size='1rem' />
              </KanbanButton>
            )}
          </Flex>
        </SimpleGrid>
      ))}

      {/* Only show Add variable button for JSON type */}
      {!readonly && !isRawType && (
        <Box>
          <KanbanButton
            variant='outline'
            leftSection={<IconPlus size='1rem' />}
            onClick={() => append({ id: undefined, name: '', jsonPath: '', hidden: false })}>
            Add variable
          </KanbanButton>
        </Box>
      )}
    </Stack>
  );
};

const ExpirationSection = ({ form, readonly }: Props) => {
  const { control } = form;
  const enableExpiration = useWatch({ control, name: 'enableExpiration' });

  return (
    <Stack gap='sm'>
      <Flex align='end' gap='md'>
        <Controller
          name='enableExpiration'
          control={control}
          render={({ field }) => (
            <KanbanCheckbox label='Variable expiration time' checked={field.value} onChange={field.onChange} disabled={readonly} />
          )}
        />

        {enableExpiration && (
          <Controller
            name='expirationTime'
            control={control}
            render={({ field }) => (
              <KanbanNumberInput
                label='Time expired'
                required
                value={field.value}
                onChange={field.onChange}
                min={1}
                placeholder='Expires in ... seconds'
                disabled={readonly}
                style={{ flex: 1 }}
              />
            )}
          />
        )}
      </Flex>
    </Stack>
  );
};

const DynamicVariableForm = ({ form, readonly }: Props) => {
  const { control, setValue } = form;
  const dataType = useWatch({ control, name: 'dataType' });
  const variableType = useWatch({ control, name: 'type' });

  const { data: executionsData } = useFetch(ExecutionApi.findByTypeNot(ExecutionTypeEnum.SQL), {
    placeholderData: (prev) => prev,
  });

  const executions = useMemo(() => {
    return executionsData?.data || [];
  }, [executionsData]);

  const executionOptions = useMemo(() => {
    return executions.map((obj: any) => ({
      value: obj.id,
      label: obj.name,
    }));
  }, [executions]);

  const {
    append: appendJsonVariable,
    fields: jsonVariableFields,
    remove: removeJsonVariable,
  } = useFieldArray({
    control,
    name: 'variableDetails',
  });

  // Ensure proper initialization of variables based on data type and variable type
  useEffect(() => {
    // Always initialize when switching to Dynamic Value, regardless of current fields
    if (variableType === VariableTypeEnum.DYNAMIC_VALUE && dataType) {
      if (jsonVariableFields.length === 0) {
        // Initialize with one variable if none exists when switching to Dynamic Value
        appendJsonVariable({ id: undefined, name: '', jsonPath: '', hidden: false });
      }
    }
  }, [variableType, dataType, jsonVariableFields.length, appendJsonVariable]);

  return (
    <Stack gap='md'>
      {/* Description */}
      <Controller
        name='description'
        control={control}
        render={({ field }) => (
          <KanbanInput
            label='Description'
            {...field}
            maxLength={MAX_DESCRIPTION_LENGTH}
            description={getMaxLengthMessage(MAX_DESCRIPTION_LENGTH)}
            disabled={readonly}
          />
        )}
      />

      {/* Execution Selection */}
      <Controller
        name='executionId'
        control={control}
        render={({ field }) => (
          <KanbanSelect
            label='Execution'
            required
            data={executionOptions}
            value={field.value}
            onChange={(value) => {
              field.onChange(value);
            }}
            searchable
            disabled={readonly}
          />
        )}
      />

      {/* Data Type Selection */}
      <Controller
        name='dataType'
        control={control}
        render={({ field }) => (
          <KanbanSelect
            label='Data type'
            required
            data={Object.values(DataTypeEnum).map((key) => ({
              value: key,
              label: DataTypeLabel[key],
            }))}
            value={field.value}
            onChange={(value) => {
              field.onChange(value);
              // Initialize variables based on data type
              if (value === DataTypeEnum.JSON) {
                // For JSON: ensure at least 1 variable with position
                if (!jsonVariableFields || jsonVariableFields.length === 0) {
                  appendJsonVariable({ id: undefined, name: '', jsonPath: '', hidden: false });
                }
              } else if (value === DataTypeEnum.RAW) {
                // For RAW: set variableDetails to empty array (not used)
                setValue('variableDetails', []);
              }
            }}
            disabled={readonly}
          />
        )}
      />

      {/* Variables Section - show for both JSON*/}
      {dataType === DataTypeEnum.JSON && (
        <JsonVariablesSection form={form} readonly={readonly} fields={jsonVariableFields} append={appendJsonVariable} remove={removeJsonVariable} />
      )}

      {/* Name field for RAW data type */}
      {dataType === DataTypeEnum.RAW && (
        <Controller
          name='name'
          control={control}
          render={({ field }) => (
            <KanbanInput
              label='Name'
              required
              {...field}
              maxLength={MAX_CHARACTER_NAME_LENGTH}
              description={getMaxLengthMessage(MAX_CHARACTER_NAME_LENGTH)}
              disabled={readonly}
              onBlur={(e) => {
                // Remove spaces and validate
                const value = e.target.value.replace(/\s/g, '');
                field.onChange(value);
                field.onBlur();
              }}
            />
          )}
        />
      )}

      {dataType === DataTypeEnum.RAW && (
        <Controller
          name='hidden'
          control={control}
          render={({ field }) => (
            <KanbanCheckbox label='Hidden' checked={field.value} onChange={field.onChange} labelPosition='left' disabled={readonly} />
          )}
        />
      )}

      {/* Variable Expiration Section */}
      <ExpirationSection form={form} readonly={readonly} />
    </Stack>
  );
};

export default DynamicVariableForm;
