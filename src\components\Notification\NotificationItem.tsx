import { Notification } from '@core/schema/Notification';
import { ActionIcon, Box, Flex, Group, Text } from '@mantine/core';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import React, { useCallback, useEffect, useState } from 'react';
import { NotificationTypeInfo } from './Constants';
import classes from './NotificationBell.module.css';
import useMutate from '@core/hooks/useMutate';
import { NotificationApi } from '@api/NotificationApi';
import { useDisclosure } from '@mantine/hooks';
import NotificationContent from './NotificationContent';

interface Props {
  notification: Notification;
  onReadNotificationSuccess: () => void;
  icon?: React.ReactNode;
}

const NotificationItem = ({ icon, notification, onReadNotificationSuccess }: Props) => {
  const { content, createdDate, title, type } = notification;
  const [isRead, setIsRead] = useState(notification.isRead);
  const [opened, { toggle }] = useDisclosure(false);
  const { mutate: readMutate } = useMutate(NotificationApi.markAsRead, {
    showLoading: false,
    errorNotification: { enable: false },
    successNotification: { enable: false },
    onSuccess: () => {
      setIsRead(true);
      onReadNotificationSuccess();
    },
  });
  useEffect(() => {
    setIsRead(notification.isRead);
  }, [notification]);

  const onClickNotification = useCallback(() => {
    if (isRead) {
      return;
    }
    readMutate(notification.id);
  }, [isRead, notification.id, readMutate]);

  const onExpandNotification = useCallback(
    (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
      event.stopPropagation();
      toggle();
    },
    [toggle],
  );

  return (
    <Group
      align='flex-start'
      gap='sm'
      p='xs'
      bg={isRead ? 'transparent' : NotificationTypeInfo[type].bg}
      className={`${classes.notificationItem}`}
      onClick={onClickNotification}>
      {icon}
      <Box style={{ flex: 1 }}>
        <Group justify='space-between' align='flex-start' wrap='nowrap'>
          <Text fw={600} size='sm' c={NotificationTypeInfo[type].color} style={{ wordBreak: 'break-word' }}>
            {title}
          </Text>
          <Flex>
            <ActionIcon variant='subtle' size='sm' onClick={onExpandNotification}>
              {opened ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
            </ActionIcon>
          </Flex>
        </Group>
        <Text size='xs' c='dimmed.1' mt={2} lineClamp={opened ? undefined : 1} style={{ wordBreak: 'break-word' }}>
          <NotificationContent content={content} />
        </Text>
        <Text size='xs' c='dimmed.1' mt={4}>
          {createdDate.fromNow()}
        </Text>
      </Box>
    </Group>
  );
};

export default NotificationItem;
