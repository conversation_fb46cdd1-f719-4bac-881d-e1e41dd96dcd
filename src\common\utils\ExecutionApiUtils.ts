import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';
import { AuthTypeEnum, FormApiInfoModel } from '@common/constants/ExecutionConstants';
import { JSONContent } from '@tiptap/react';
import { EXECUTION_API_AUTO_GENERATE } from '@pages/admins/executionConfig/Constants';
import { UseFieldArrayReturn, UseFormSetValue } from 'react-hook-form';
import { klona } from 'klona';
import { DEFAULT_KEY_VALUE } from '@pages/admins/executionConfig/tabs/execution/ExcecutionDefaultValues';
/**

 * Filters form data to only include rows with meaningful content
 * @param data - Array of form data objects
 * @returns Filtered array with only meaningful data
 */
export function filterMeaningfulFormData<T extends { key?: string; value?: string; description?: string }>(data: T[]): T[] {
  return data.filter(({ key, value }) => !!key?.trim() && !!value?.trim());
}

/**
 * Prepares API info data for submission by filtering out empty rows
 * @param apiInfo - The API info object
 * @returns Cleaned API info object
 */

export function prepareApiInfoForSubmission(apiInfo: ExecutionApiInfoModel) {
  if (!apiInfo) {
    return apiInfo;
  }
  const cleaned = klona(apiInfo);
  if (cleaned.params) {
    cleaned.params = filterMeaningfulFormData(cleaned.params);
  }
  if (cleaned.headers) {
    cleaned.headers = filterMeaningfulFormData(cleaned.headers);
  }
  if (cleaned.body?.formUrlEncoded) {
    cleaned.body.formUrlEncoded = filterMeaningfulFormData(cleaned.body.formUrlEncoded);
  }
  return cleaned;
}

/**
 * Authentication utilities
 */
export function generateAuthHeader(authType: AuthTypeEnum, credentials: { token?: string; username?: string; password?: string }): string | null {
  switch (authType) {
    case AuthTypeEnum.BEARER:
      return credentials.token ? `Bearer ${credentials.token}` : null;

    case AuthTypeEnum.BASIC:
      if (credentials.username && credentials.password) {
        try {
          // Encode Unicode safely and convert to base64
          const encoded = safeBtoa(`${credentials.username}:${credentials.password}`);
          return `Basic ${encoded}`;
        } catch (error) {
          console.info('Failed to encode Basic Auth credentials:', error);
          return null;
        }
      }
      return null;
    case AuthTypeEnum.NONE:
    default:
      return null;
  }
}

export function safeBtoa(str: string): string {
  const utf8Bytes = new TextEncoder().encode(str);
  const binary = Array.from(utf8Bytes)
    .map((byte) => String.fromCharCode(byte))
    .join('');
  return btoa(binary);
}

export function extractTextFromMentionJSON(node: JSONContent): string {
  if (!node) {
    return '';
  }
  if (node.type === 'mention') {
    return `{{${node?.attrs?.label}}}`;
  }
  if (node.type === 'text') {
    if (!node.text) {
      return '';
    }
    return node.text;
  }
  if (node.content) {
    return node.content.map(extractTextFromMentionJSON).join('');
  }
  return '';
}

export function convertTextToMentions(text: string): JSONContent {
  const mentionPattern = /\{\{([^}]+)\}\}/g;
  const matches = [...text.matchAll(mentionPattern)];
  const content: JSONContent[] = [];
  let lastIndex = 0;

  matches.forEach((match) => {
    if (!match || match.index === undefined) {
      return null;
    }
    const start = match.index;
    const end = start + match[0].length;

    if (start > lastIndex) {
      const raw = text.slice(lastIndex, start).replace(mentionPattern, (_, m) => `@${m}`);
      content.push({ type: 'text', text: raw });
    }

    const label = match[1];
    content.push({
      type: 'mention',
      attrs: { id: label, label },
    });

    lastIndex = end;
  });

  if (lastIndex < text.length) {
    content.push({
      type: 'text',
      text: text.slice(lastIndex),
    });
  }

  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content,
      },
    ],
  };
}

function ensureOneEmptyRow(headersFA: UseFieldArrayReturn<FormApiInfoModel, 'apiInfo.headers'>) {
  const headers = headersFA.fields;

  let emptyRowsCount = 0;
  for (let i = headers.length - 1; i >= 0; i--) {
    const row = headers[i];
    if (!row?.key && !row?.value && !row?.description) {
      emptyRowsCount++;
    } else {
      break;
    }
  }

  while (emptyRowsCount > 1) {
    headersFA.remove(headers.length - 1);
    emptyRowsCount--;
  }

  // Add one empty row if none exists
  if (emptyRowsCount === 0) {
    headersFA.append(DEFAULT_KEY_VALUE);
  }
}

export function upsertAutoHeader({
  headersFA,
  key,
  setValue,
  value,
}: {
  key: string;
  value: string;
  headersFA: UseFieldArrayReturn<FormApiInfoModel, 'apiInfo.headers'>;
  setValue: UseFormSetValue<FormApiInfoModel>;
}) {
  const headers = headersFA.fields;
  const existingIndex = headers.findIndex((h) => h.key?.toLowerCase() === key.toLowerCase());

  if (value) {
    if (existingIndex >= 0) {
      const existing = headers[existingIndex];
      if (existing?.value !== value && existing?.autoGenerated === true) {
        setValue(`apiInfo.headers.${existingIndex}.key`, key);
        setValue(`apiInfo.headers.${existingIndex}.value`, value);
        setValue(`apiInfo.headers.${existingIndex}.enable`, true);
        setValue(`apiInfo.headers.${existingIndex}.description`, EXECUTION_API_AUTO_GENERATE);
        setValue(`apiInfo.headers.${existingIndex}.autoGenerated`, true);
      }
    } else {
      headersFA.insert(0, {
        key,
        value,
        description: EXECUTION_API_AUTO_GENERATE,
        enable: true,
        autoGenerated: true,
      });
    }

    // Ensure exactly one empty row at the end
    ensureOneEmptyRow(headersFA);
  }
}
