import { JSONContent } from '@tiptap/react';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Mention from '@tiptap/extension-mention';
import CharacterCount from '@tiptap/extension-character-count';

/**
 * Common TipTap editor extensions configuration
 */
export const getBaseEditorExtensions = () => [
  Document,
  Paragraph,
  Text,
];

/**
 * Get character count extension with limit
 */
export const getCharacterCountExtension = (limit: number) => 
  CharacterCount.configure({ limit });

/**
 * Parse string to TipTap JSON content
 * Handles both JSON strings and plain text
 */
export function parseStringToJson(str: string): JSONContent {
  if (typeof str !== 'string') {
    if (str === null || str === undefined) {
      str = '';
    } else {
      str = String(str);
    }
  }

  // Handle empty string
  if (!str || str.trim() === '') {
    return {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [],
        },
      ],
    };
  }

  try {
    // Try to parse as JSON first (for editor content)
    const parsed = JSON.parse(str);
    if (parsed && typeof parsed === 'object' && parsed.type === 'doc') {
      return parsed;
    }
    // If it's valid JSON but not editor format, treat as plain text
    return parseTextWithVariables(str);
  } catch (e) {
    // If not JSON, parse as text with variables
    return parseTextWithVariables(str);
  }
}

/**
 * Parse text with variable patterns ({{variable}} or @variable)
 */
export function parseTextWithVariables(text: string, variablePattern = /\{\{\s*([^}]+)\s*\}\}/g): JSONContent {
  const content: JSONContent[] = [];
  let lastIndex = 0;
  let match;

  while ((match = variablePattern.exec(text)) !== null) {
    // Add text before the variable
    if (match.index > lastIndex) {
      const textBefore = text.slice(lastIndex, match.index);
      if (textBefore) {
        content.push({
          type: 'text',
          text: textBefore,
        });
      }
    }

    // Add the variable as a mention
    content.push({
      type: 'mention',
      attrs: {
        id: match[1].trim(),
        label: match[1].trim(),
      },
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex);
    if (remainingText) {
      content.push({
        type: 'text',
        text: remainingText,
      });
    }
  }

  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: content.length > 0 ? content : [{ type: 'text', text: text }],
      },
    ],
  };
}

/**
 * Format TipTap content to backend string format
 */
export function formatContentToBackend(content: JSONContent[], mentionPrefix = '{{'): string {
  return content
    .map((node) => {
      if (node.type === 'text' && node.text) {
        return node.text;
      } else if (node.type === 'mention' && node.attrs?.id && node.attrs?.label) {
        if (mentionPrefix === '{{') {
          return `{{${node.attrs.id}}}`;
        } else {
          return `@${node.attrs.id}`;
        }
      } else if (node.content && Array.isArray(node.content)) {
        return formatContentToBackend(node.content, mentionPrefix);
      }
      return '';
    })
    .join('');
}

/**
 * Format TipTap content to display string format
 */
export function formatContentToString(content: JSONContent[], mentionPrefix = '@'): string {
  return content
    .map((node) => {
      if (node.type === 'text' && node.text) {
        return node.text;
      } else if (node.type === 'mention' && node.attrs?.id && node.attrs?.label) {
        return `${mentionPrefix}${node.attrs.id}`;
      } else if (node.content && Array.isArray(node.content)) {
        return formatContentToString(node.content, mentionPrefix);
      }
      return '';
    })
    .join('');
}

/**
 * Common keyboard navigation handler for mention lists
 */
export function createKeyboardNavigationHandler(
  allItems: any[],
  hoverIndex: number,
  setHoverIndex: (index: number) => void,
  handleCommand: (index: number) => void
) {
  return ({ event }: { event: KeyboardEvent }) => {
    const { key } = event;

    if (key === 'ArrowUp') {
      setHoverIndex(Math.max(hoverIndex - 1, 0));
      return true;
    }

    if (key === 'ArrowDown') {
      setHoverIndex(Math.min(hoverIndex + 1, allItems.length - 1));
      return true;
    }

    if (key === 'Enter') {
      handleCommand(hoverIndex);
      return true;
    }

    return false;
  };
}

/**
 * Common mention extension configuration
 */
export function getMentionExtension(config: {
  char: string;
  HTMLAttributes?: Record<string, any>;
  renderComponent: any;
}) {
  return Mention.configure({
    HTMLAttributes: config.HTMLAttributes || {},
    suggestion: {
      char: config.char,
      render: config.renderComponent,
    },
  });
}

/**
 * Common paste handler for character limit
 */
export function createPasteHandler(maxLength: number) {
  return (view: any, event: ClipboardEvent, editor: any) => {
    if (!editor) {
      return false;
    }
    
    const currentContentLength = editor.storage.characterCount.characters();
    const pastedText = event.clipboardData?.getData('text/plain') || '';
    const pastedLength = pastedText.length;
    
    if (currentContentLength + pastedLength > maxLength) {
      const remainingLength = maxLength - currentContentLength;
      if (remainingLength <= 0) {
        event.preventDefault();
        return true;
      }
      
      const truncatedText = pastedText.slice(0, remainingLength);
      const insertPos = view.state.selection.from;

      editor.commands.insertContentAt(insertPos, truncatedText);
      event.preventDefault();
      return true;
    }
    
    return false;
  };
}

/**
 * Common editor props configuration
 */
export function getBaseEditorProps(maxLength: number, disabled: boolean, className: string) {
  return {
    attributes: {
      class: `${className} ${disabled ? 'disabled' : ''}`,
    },
    handleDOMEvents: {
      paste: createPasteHandler(maxLength),
    },
  };
}
