import { z } from 'zod';
import {
  MAX_NAME_LENGTH,
  MAX_DESCRIPTION_LENGTH,
  MAX_RECIPIENT_LENGTH,
  WEB_MONITOR_WEB_URL_MAX_LENGTH,
  WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE,
  WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE,
  WEB_MONITOR_WEB_WAIT_MIN_VALUE,
  WEB_MONITOR_WEB_WAIT_MAX_VALUE,
  INDENTIFIER_MAX_LENGTH,
  MAX_OF_OTHERS_ACTION_VALUE,
} from '@common/constants/ValidationConstant';
import { ActionTypeEnum, BrowserEnum, FindElementByEnum, MonitorTypeEnum } from '@common/constants/WebMonitorConfigConstant';
import { IDENTIFIER_SHOULD_DISABLE, MAX_LENGTH_BY_ACTION_TYPE, VALUE_SHOULD_REQUIRED, WAIT_TYPE } from '@common/constants/MonitorActionRules';

export const ActionModelSchema = z
  .object({
    id: z.string().optional(),
    actionId: z.string().optional(),
    actionType: z.nativeEnum(ActionTypeEnum),
    findElementBy: z.nativeEnum(FindElementByEnum),
    identifier: z.string().max(INDENTIFIER_MAX_LENGTH).optional(),
    value: z.string().optional(),
    orders: z.number().optional(),
  })
  .superRefine((data, ctx) => {
    const { actionType, identifier, value } = data;
    // Check if value is required
    if (VALUE_SHOULD_REQUIRED.includes(actionType)) {
      if (!value || value.trim() === '') {
        ctx.addIssue({
          path: ['value'],
          code: z.ZodIssueCode.custom,
          message: 'Value is required for this action type.',
        });
      } else if (WAIT_TYPE.includes(actionType)) {
        const parsed = Number(value);
        if (!Number.isInteger(parsed) || parsed < WEB_MONITOR_WEB_WAIT_MIN_VALUE || parsed > WEB_MONITOR_WEB_WAIT_MAX_VALUE) {
          ctx.addIssue({
            path: ['value'],
            code: z.ZodIssueCode.custom,
            message: 'Value must be a number and must be between 1-600.',
          });
        }
      }
    }

    if (!IDENTIFIER_SHOULD_DISABLE.includes(actionType) && (!identifier || identifier.trim() === '')) {
      ctx.addIssue({
        path: ['identifier'],
        code: z.ZodIssueCode.custom,
        message: 'Identifier is required for this action type.',
      });
    }

    const maxLength = MAX_LENGTH_BY_ACTION_TYPE[actionType] ?? MAX_OF_OTHERS_ACTION_VALUE;

    if (value && value.length > maxLength) {
      ctx.addIssue({
        path: ['value'],
        code: z.ZodIssueCode.too_big,
        message: `Value must be at most ${maxLength} characters.`,
        maximum: maxLength,
        type: 'string',
        inclusive: true,
      });
    }
  });

export type ActionModel = z.infer<typeof ActionModelSchema>;

export const WebMonitorConfigModelSchema = z.object({
  id: z.string().optional(),
  name: z.string().trim().min(1).max(MAX_NAME_LENGTH),
  description: z.string().trim().max(MAX_DESCRIPTION_LENGTH).optional(),
  webUrl: z.string().trim().min(1).max(WEB_MONITOR_WEB_URL_MAX_LENGTH),
  monitorType: z.nativeEnum(MonitorTypeEnum),
  timeout: z.number().min(WEB_MONITOR_WEB_TIMEOUT_MIN_VALUE).max(WEB_MONITOR_WEB_TIMEOUT_MAX_VALUE),
  browser: z.nativeEnum(BrowserEnum),
  authActions: z.array(ActionModelSchema).optional(),
  actions: z.array(ActionModelSchema),
  serviceId: z.string().min(1),
  applicationId: z.string().min(1),
  priorityId: z.number(),
  serviceName: z.string().optional(),
  applicationName: z.string().optional(),
  content: z.string().trim().min(1),
  contentJson: z.string().trim().min(1),
  contact: z.string().trim().min(1).max(MAX_RECIPIENT_LENGTH),
  active: z.boolean(),
  months: z.array(z.number()).optional(),
  dayOfMonths: z.array(z.number()).optional(),
  dayOfWeeks: z.array(z.number()).optional(),
  hours: z.array(z.number()).optional(),
  minutes: z.array(z.number()).optional(),
});

export type WebMonitorConfigModel = z.infer<typeof WebMonitorConfigModelSchema>;
