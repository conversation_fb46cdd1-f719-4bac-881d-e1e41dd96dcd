/* eslint-disable react/prop-types */
import Document from '@tiptap/extension-document';
import Mention from '@tiptap/extension-mention';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import { Editor<PERSON>onte<PERSON>, J<PERSON><PERSON><PERSON>nt, React<PERSON><PERSON>er, useEditor } from '@tiptap/react';
import React, { useEffect, useRef, useState } from 'react';
import '@mantine/tiptap/styles.css';
import { KanbanText } from 'kanban-design-system';
import styles from './CustomVariableInput.module.scss';
import tippy, { GetReferenceClientRect, Instance, Props } from 'tippy.js';
import CharacterCount from '@tiptap/extension-character-count';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import { VariableMentionListActions, VariableMentionListProps, VariableMentionList } from './VariableMentionList';

interface EditorProps {
  value: string;
  onChange: (content: string, value?: string) => void;
  label?: string;
  disabled?: boolean;
  required?: boolean;
  maxLength?: number;
  error?: string;
}

// For backend - format as {{variable}}
function formatContentToBackend(content: JSONContent[]): string {
  return content
    .map((node) => {
      if (node.type === 'text' && node.text) {
        return node.text;
      } else if (node.type === 'mention' && node.attrs?.id && node.attrs?.label) {
        return `{{${node.attrs.id}}}`;
      } else if (node.content && Array.isArray(node.content)) {
        return formatContentToBackend(node.content);
      }
      return '';
    })
    .join('');
}

function parseStringToJson(str: string) {
  try {
    return JSON.parse(str);
  } catch (e) {
    // If not JSON, try to parse as text with {{ variable }} patterns
    return parseTextWithVariables(str);
  }
}

function parseTextWithVariables(text: string): JSONContent {
  const content: JSONContent[] = [];
  const variablePattern = /\{\{\s*([^}]+)\s*\}\}/g;
  let lastIndex = 0;
  let match;

  while ((match = variablePattern.exec(text)) !== null) {
    // Add text before the variable
    if (match.index > lastIndex) {
      const textBefore = text.slice(lastIndex, match.index);
      if (textBefore) {
        content.push({
          type: 'text',
          text: textBefore,
        });
      }
    }

    // Add the variable as a mention
    content.push({
      type: 'mention',
      attrs: {
        id: match[1].trim(),
        label: match[1].trim(),
      },
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex);
    if (remainingText) {
      content.push({
        type: 'text',
        text: remainingText,
      });
    }
  }

  return {
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: content.length > 0 ? content : [{ type: 'text', text: text }],
      },
    ],
  };
}

const DEFAULT_MAX_LENGTH = 500;

export const CustomVariableInput: React.FC<EditorProps> = ({
  disabled = false,
  error,
  label,
  maxLength = DEFAULT_MAX_LENGTH,
  onChange,
  required = false,
  value,
}) => {
  const hasHydrated = useRef(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Load variables from API
  const { data: variableData, refetch } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Debug API response
  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log('=== VARIABLE DATA CHANGED ===');
    // eslint-disable-next-line no-console
    console.log('variableData:', variableData);
    // eslint-disable-next-line no-console
    console.log('variableData?.data:', variableData?.data);
    // eslint-disable-next-line no-console
    console.log('Array.isArray(variableData?.data):', Array.isArray(variableData?.data));
    if (variableData?.data) {
      // eslint-disable-next-line no-console
      console.log('Variables count:', variableData.data.length);
      // eslint-disable-next-line no-console
      console.log('First variable:', variableData.data[0]);
    }
  }, [variableData]);

  // Function to get current variables
  const getCurrentVariables = () => {
    const vars = variableData?.data || [];
    // eslint-disable-next-line no-console
    console.log('getCurrentVariables called, returning:', vars);
    return vars;
  };

  // Force refetch on mount
  useEffect(() => {
    // eslint-disable-next-line no-console
    console.log('Component mounted, forcing refetch...');
    refetch();
  }, [refetch]);

  const editor = useEditor({
    editable: !disabled,
    content: parseStringToJson(value),
    editorProps: {
      attributes: {
        class: `${styles.editor} ${disabled ? styles.disabled : ''}`,
      },
      handleDOMEvents: {
        paste: (view, event: ClipboardEvent) => {
          if (!editor) {
            return false;
          }
          const currentContentLength = editor.storage.characterCount.characters();
          const pastedText = event.clipboardData?.getData('text/plain') || '';
          const pastedLength = pastedText.length;
          if (currentContentLength + pastedLength > maxLength) {
            const remainingLength = maxLength - currentContentLength;
            if (remainingLength <= 0) {
              event.preventDefault();
              return true;
            }
            const truncatedText = pastedText.slice(0, remainingLength);
            const insertPos = view.state.selection.from;

            editor.commands.insertContentAt(insertPos, truncatedText);
            event.preventDefault();
            return true;
          }
          return false;
        },
        beforeinput: (view, event: InputEvent) => {
          if (!editor) {
            return false;
          }
          const json = editor.getJSON();
          const currentLength = formatContentToBackend(json.content as JSONContent[]).length;
          const inputData = (event as InputEvent).data || '';
          const selectionLength = view.state.selection.to - view.state.selection.from;

          const newLength = currentLength - selectionLength + inputData.length;

          if (newLength > maxLength) {
            event.preventDefault();
            setErrorMessage(`Content cannot exceed ${maxLength} characters.`);
            setTimeout(() => setErrorMessage(''), 3000);
            return true;
          }
          // Clear any existing error message
          setErrorMessage('');
          return false;
        },
      },
    },
    extensions: [
      Document,
      Paragraph,
      Text,
      CharacterCount.configure({
        limit: maxLength,
      }),
      Mention.configure({
        HTMLAttributes: {
          class: styles.mention,
        },
        suggestion: {
          char: '@@', // Changed from @ to @@
          items: ({ query }) => {
            const currentVars = getCurrentVariables();
            // eslint-disable-next-line no-console
            console.log('QUERY:', query);
            // eslint-disable-next-line no-console
            console.log('CURRENT VARIABLES:', currentVars);

            return currentVars
              .filter((v) => v.name.toLowerCase().includes(query.toLowerCase()))
              .slice(0, 10)
              .map((v) => ({
                id: v.name,
                label: v.name,
              }));
          },

          render: () => {
            let component: ReactRenderer<VariableMentionListActions, VariableMentionListProps>;
            let popup: Instance<Props>[];

            return {
              onStart: (props) => {
                component = new ReactRenderer(VariableMentionList, {
                  props,
                  editor: props.editor,
                });

                if (!props.clientRect) {
                  return;
                }

                popup = tippy('body', {
                  getReferenceClientRect: props.clientRect as GetReferenceClientRect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: 'manual',
                  placement: 'bottom-start',
                });
              },

              onUpdate(props) {
                component.updateProps(props);

                if (!props.clientRect) {
                  return;
                }

                popup[0].setProps({
                  getReferenceClientRect: props.clientRect as GetReferenceClientRect,
                });
              },

              onKeyDown(props) {
                if (props.event.key === 'Escape') {
                  popup[0].hide();

                  return true;
                }

                return component.ref?.onKeyDown(props) || false;
              },

              onExit() {
                popup[0].destroy();
                component.destroy();
              },
            };
          },
        },
      }),
    ],
    onUpdate: ({ editor }) => {
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    },
  });

  useEffect(() => {
    if (editor && !hasHydrated.current) {
      hasHydrated.current = true;
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    }
  }, [editor, onChange]);

  return (
    <div className={styles.container}>
      {label && (
        <KanbanText size='sm' fw={500} className={styles.label}>
          {label}
          {required && <span className={styles.required}>*</span>}
        </KanbanText>
      )}
      <div className={`${styles.editorWrapper} ${disabled ? styles.disabledWrapper : ''}`}>
        <EditorContent editor={editor} />
      </div>
      {(errorMessage || error) && (
        <KanbanText size='xs' c='red' mt={4}>
          {errorMessage || error}
        </KanbanText>
      )}
    </div>
  );
};
