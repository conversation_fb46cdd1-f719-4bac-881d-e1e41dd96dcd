import { EditorContent, useEditor, <PERSON><PERSON><PERSON>ontent, React<PERSON>enderer } from '@tiptap/react';
import React, { useEffect, useRef, useState } from 'react';
import '@mantine/tiptap/styles.css';
import styles from './CustomVariableInput.module.scss';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import { VariableMentionListActions, VariableMentionListProps, VariableMentionList } from './VariableMentionList';
import { parseStringToJson, formatContentToBackend } from '../../../utils/tiptapEditorUtils';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Mention from '@tiptap/extension-mention';
import CharacterCount from '@tiptap/extension-character-count';
import tippy, { GetReferenceClientRect, Instance, Props } from 'tippy.js';
import { KanbanText } from 'kanban-design-system';

interface EditorProps {
  value: string;
  onChange: (content: string, value?: string) => void;
  label?: string;
  disabled?: boolean;
  required?: boolean;
  maxLength?: number;
  error?: string;
}

const DEFAULT_MAX_LENGTH = 500;

export const CustomVariableInput: React.FC<EditorProps> = ({
  disabled = false,
  error,
  label,
  maxLength = DEFAULT_MAX_LENGTH,
  onChange,
  required = false,
  value,
}) => {
  const hasHydrated = useRef(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Load variables from API
  const { refetch } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Force refetch on mount
  useEffect(() => {
    refetch();
  }, [refetch]);

  const editor = useEditor({
    editable: !disabled,
    content: parseStringToJson(value),
    editorProps: {
      attributes: {
        class: `${styles.editor} ${disabled ? styles.disabled : ''}`,
      },
      handleDOMEvents: {
        paste: (view, event: ClipboardEvent) => {
          if (!editor) {
            return false;
          }
          const currentContentLength = editor.storage.characterCount.characters();
          const pastedText = event.clipboardData?.getData('text/plain') || '';
          const pastedLength = pastedText.length;
          if (currentContentLength + pastedLength > maxLength) {
            const remainingLength = maxLength - currentContentLength;
            if (remainingLength <= 0) {
              event.preventDefault();
              return true;
            }
            const truncatedText = pastedText.slice(0, remainingLength);
            const insertPos = view.state.selection.from;

            editor.commands.insertContentAt(insertPos, truncatedText);
            event.preventDefault();
            return true;
          }
          return false;
        },
        beforeinput: (view, event: InputEvent) => {
          if (!editor) {
            return false;
          }
          const json = editor.getJSON();
          const currentLength = formatContentToBackend(json.content as JSONContent[]).length;
          const inputData = (event as InputEvent).data || '';
          const selectionLength = view.state.selection.to - view.state.selection.from;

          const newLength = currentLength - selectionLength + inputData.length;

          if (newLength > maxLength) {
            event.preventDefault();
            setErrorMessage(`Content cannot exceed ${maxLength} characters.`);
            setTimeout(() => setErrorMessage(''), 3000);
            return true;
          }
          // Clear any existing error message
          setErrorMessage('');
          return false;
        },
      },
    },
    extensions: [
      Document,
      Paragraph,
      Text,
      CharacterCount.configure({
        limit: maxLength,
      }),
      Mention.configure({
        HTMLAttributes: {
          class: styles.mention,
        },
        suggestion: {
          char: '@@',

          render: () => {
            let component: ReactRenderer<VariableMentionListActions, VariableMentionListProps>;
            let popup: Instance<Props>[];

            return {
              onStart({ clientRect, editor, ...restProps }) {
                component = new ReactRenderer(VariableMentionList, {
                  props: { editor, clientRect, ...restProps },
                  editor,
                });

                if (!clientRect) {
                  return;
                }

                popup = tippy('body', {
                  getReferenceClientRect: clientRect as GetReferenceClientRect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: 'manual',
                  placement: 'bottom-start',
                });
              },

              onUpdate({ clientRect, ...restProps }) {
                component.updateProps({ clientRect, ...restProps });

                if (!clientRect) {
                  return;
                }

                popup[0].setProps({
                  getReferenceClientRect: clientRect as GetReferenceClientRect,
                });
              },

              onKeyDown({ event, ...restProps }) {
                if (event.key === 'Escape') {
                  popup[0].hide();

                  return true;
                }

                return component.ref?.onKeyDown({ event, ...restProps }) || false;
              },

              onExit() {
                popup[0].destroy();
                component.destroy();
              },
            };
          },
        },
      }),
    ],
    onUpdate: ({ editor }) => {
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    },
  });

  useEffect(() => {
    if (editor && !hasHydrated.current) {
      hasHydrated.current = true;
      const json = editor.getJSON();
      const backendText = formatContentToBackend(json.content as JSONContent[]);
      onChange(JSON.stringify(json), backendText);
    }
  }, [editor, onChange]);

  // Update editor content when value prop changes (for data from BE)
  useEffect(() => {
    if (editor && hasHydrated.current) {
      const currentContent = JSON.stringify(editor.getJSON());
      const newContent = JSON.stringify(parseStringToJson(value));

      // Only update if content actually changed to avoid infinite loops
      if (currentContent !== newContent) {
        editor.commands.setContent(parseStringToJson(value));
      }
    }
  }, [editor, value]);

  return (
    <div className={styles.container}>
      {label && (
        <KanbanText size='sm' fw={500} className={styles.label}>
          {label}
          {required && <span className={styles.required}>*</span>}
        </KanbanText>
      )}
      <div className={`${styles.editorWrapper} ${disabled ? styles.disabledWrapper : ''}`}>
        <EditorContent editor={editor} />
      </div>
      {(errorMessage || error) && (
        <KanbanText size='xs' c='red' mt={4}>
          {errorMessage || error}
        </KanbanText>
      )}
    </div>
  );
};
