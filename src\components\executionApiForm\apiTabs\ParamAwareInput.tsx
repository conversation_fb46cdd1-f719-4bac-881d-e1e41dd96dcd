import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Box } from '@mantine/core';
import { EditorContent } from '@tiptap/react';
import { mentionPopupState, useMentionEditor } from './MentionEditor';
import classes from '../ApiConfigSession.module.css';
import clsx from 'clsx';

interface ParamAwareInputProps {
  disabled?: boolean;
  executionParams?: string[];
  field: {
    name: string;
    value: string;
    onBlur?: () => void;
    onChange: (e: { target: { value: string } }) => void;
  };
  registerEditor?: (fieldName: string, insertFn: (text: string, pos: number) => void) => void;
  isUrl?: boolean;
  maxLength?: number;
}

const ParamAwareInput = forwardRef<{ focus: () => void }, ParamAwareInputProps>(
  ({ disabled, executionParams, field, isUrl = false, maxLength, registerEditor }, ref) => {
    const editor = useMentionEditor({ field, disabled, registerEditor, executionParams, maxLength });
    const [focused, setFocused] = useState(false);

    useImperativeHandle(ref, () => ({
      focus: () => {
        editor?.commands?.focus?.();
      },
    }));

    return (
      <Box
        className={clsx(classes.inputBox, {
          [classes.focused]: focused,
          [classes.isUrl]: isUrl,
        })}
        onFocus={() => setFocused(true)}
        onBlur={() => {
          if (!mentionPopupState.isSelecting) {
            setFocused(false);
            field.onBlur?.();
          }
        }}>
        <EditorContent
          editor={editor}
          className={clsx(classes.editorContent, {
            [classes.focused]: focused,
          })}
        />
      </Box>
    );
  },
);

ParamAwareInput.displayName = 'ParamAwareInput';
export default ParamAwareInput;
