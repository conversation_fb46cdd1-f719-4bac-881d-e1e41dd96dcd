import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Box, Flex, Group, Stack, Title } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import classes from './GroupConfigStyle.module.css';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { Controller, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  AutoTriggerActionConfigTypeEnum,
  AutoTriggerConfigAction,
  DEFAULT_FORM_VALUE,
  DEFAULT_GROUP_CONDITION,
  DEFAULT_TIME_LAST_TRIGGER_VALUE,
  TypeOptions,
} from './Constants';
import {
  DESCRIPTION_MAX_LENGTH,
  MAX_ENABLE_TIME_SINCE_LAST_TRIGGER,
  MAX_NAME_LENGTH,
  MAX_TIME_SINCE_LAST_TRIGGER,
  MIN_TIME_SINCE_LAST_TRIGGER,
} from '@common/constants/ValidationConstant';
import { AclPermission } from '@models/AclPermission';
import GuardComponent from '@components/GuardComponent';
import { AutoTriggerActionConfigApi } from '@api/AutoTriggerActionConfigApi';
import { AutoTriggerActionConfigModel, AutoTriggerActionConfigModelSchema } from '@models/AutoTriggerActionConfigModel';
import ReferenceSession from './ReferenceSession';
import ConditionSession from './ConditionSession';
import ExecutionAction from './Action';
import { validateQuery } from '@components/queryBuilder';
import { RuleGroupType } from 'react-querybuilder';
import TimeType from './TimeType';
interface SessionProps {
  title: string;
  children: React.ReactNode;
}

const SaveButton = ({ form }: { form: UseFormReturn<AutoTriggerActionConfigModel> }) => {
  const {
    control,
    formState: { isValid },
    getValues,
  } = form;
  const ruleGroup = useWatch({ control: control, name: 'ruleGroup' });
  const triggerType = useWatch({ control: control, name: 'triggerType' });
  const isRuleValid = triggerType === AutoTriggerActionConfigTypeEnum.CONDITION ? ruleGroup && validateQuery(ruleGroup as RuleGroupType) : true;

  const navigate = useNavigate();
  const { mutate: saveAutoTriggerActionConfigMutate } = useMutate(AutoTriggerActionConfigApi.save, {
    onSuccess: () => {
      navigate('../');
    },
  });

  const onSave = useCallback(() => {
    const parsedData = AutoTriggerActionConfigModelSchema.safeParse(getValues());

    if (parsedData.success) {
      const { applications, executions, services, ...rest } = parsedData.data;

      saveAutoTriggerActionConfigMutate({
        ...rest,
        serviceIds: services?.map((ele) => ele.id) || [],
        applicationIds: applications?.map((ele) => ele.id) || [],
        executionIds: (executions?.map((ele) => ele.id).filter(Boolean) as string[]) || [],
      });
    }
  }, [getValues, saveAutoTriggerActionConfigMutate]);

  return (
    <GuardComponent requirePermissions={[AclPermission.autoTriggerActionConfigEdit, AclPermission.autoTriggerActionConfigCreate]}>
      <KanbanButton onClick={onSave} disabled={!isValid || !isRuleValid}>
        Save
      </KanbanButton>
    </GuardComponent>
  );
};

const AutoTriggerActionConfigFormPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id } = useParams<{ id?: string }>();
  const autoTriggerActionConfigId = id && id !== '0' ? id : '';
  const isCreateMode = !autoTriggerActionConfigId;
  const [currentTab, setCurrentTab] = useState<string>(AutoTriggerConfigAction.CREATE);
  const isViewMode = currentTab === AutoTriggerConfigAction.VIEW;

  const form = useForm<AutoTriggerActionConfigModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(AutoTriggerActionConfigModelSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    const tab = searchParams.get('action');
    if (tab) {
      setCurrentTab(tab);
    }
  }, [searchParams]);

  const { data: autoTriggerActionConfigData } = useFetch(AutoTriggerActionConfigApi.findById(autoTriggerActionConfigId), {
    enabled: !isCreateMode,
  });
  const { control, reset } = form;
  const triggerType = useWatch({ control, name: 'triggerType' });

  const Session = ({ children, title }: SessionProps) => (
    <Stack className={classes.session} gap='xs'>
      <Title order={5}>{title}</Title>
      {children}
    </Stack>
  );

  useEffect(() => {
    if (!isCreateMode && autoTriggerActionConfigData?.data) {
      const { data } = autoTriggerActionConfigData;

      reset({
        ...data,
        ruleGroup: data.ruleGroup || DEFAULT_GROUP_CONDITION,
        timeSinceLastTrigger: data.timeSinceLastTrigger ?? DEFAULT_TIME_LAST_TRIGGER_VALUE,
      });
    }
  }, [autoTriggerActionConfigData, form, isCreateMode, reset]);

  return (
    <Box>
      <Flex justify='space-between' mb='sm' className={classes.groupConfigHeader}>
        <Title order={3}>
          {isViewMode ? 'View auto trigger action config' : isCreateMode ? 'Create auto trigger action config' : 'Update auto trigger action config'}
        </Title>
        <Flex>
          <Group>
            <KanbanButton variant='outline' onClick={() => navigate('../')}>
              Cancel
            </KanbanButton>
            {!isViewMode && <SaveButton form={form} />}
          </Group>
        </Flex>
      </Flex>

      <Stack gap='md'>
        <Session title='General Information'>
          <Controller
            control={control}
            name='name'
            render={({ field, fieldState }) => (
              <KanbanInput
                disabled={isViewMode}
                label='Config name'
                required
                {...field}
                maxLength={MAX_NAME_LENGTH}
                error={fieldState.error?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='description'
            render={({ field, fieldState }) => (
              <KanbanInput
                disabled={isViewMode}
                label='Description'
                {...field}
                maxLength={DESCRIPTION_MAX_LENGTH}
                error={fieldState.error?.message}
              />
            )}
          />
          <Controller
            control={control}
            name='triggerType'
            render={({ field: { onChange, value } }) => (
              <KanbanSelect
                label='Trigger by'
                placeholder='Select action type'
                data={TypeOptions}
                value={value}
                disabled={isViewMode}
                required
                allowDeselect={false}
                onChange={(val) => {
                  onChange(val as AutoTriggerActionConfigTypeEnum);
                }}
              />
            )}
          />
        </Session>

        {triggerType === AutoTriggerActionConfigTypeEnum.CONDITION && (
          <>
            <Session title='Reference'>
              <ReferenceSession isViewMode={isViewMode} form={form} />
            </Session>

            <Session title='Condition'>
              <ConditionSession isViewMode={isViewMode} form={form} />
            </Session>

            <Session title='Trigger config'>
              <Controller
                control={control}
                name='timeSinceLastTrigger'
                render={({ field, fieldState }) => (
                  <KanbanNumberInput
                    disabled={isViewMode}
                    label='Time since last trigger'
                    max={MAX_ENABLE_TIME_SINCE_LAST_TRIGGER}
                    allowDecimal={false}
                    allowNegative={false}
                    required
                    error={fieldState.error?.message}
                    {...field}
                    clampBehavior='strict'
                    onChange={(value) => {
                      const val = Number(value);
                      field.onChange(isNaN(val) ? undefined : val);
                    }}
                    onBlur={() => {
                      const val = Number(field.value);
                      if (isNaN(val) || val < MIN_TIME_SINCE_LAST_TRIGGER) {
                        field.onChange(MIN_TIME_SINCE_LAST_TRIGGER);
                      }
                      if (val > MAX_TIME_SINCE_LAST_TRIGGER) {
                        field.onChange(MAX_TIME_SINCE_LAST_TRIGGER);
                      }
                    }}
                  />
                )}
              />
            </Session>
          </>
        )}

        {triggerType === AutoTriggerActionConfigTypeEnum.TIME && (
          <Session title='Time Type'>
            <TimeType isViewMode={isViewMode} form={form} />
          </Session>
        )}

        <Session title='Action'>
          <ExecutionAction isViewMode={isViewMode} form={form} />
        </Session>
      </Stack>
    </Box>
  );
};

export default AutoTriggerActionConfigFormPage;
