import { z } from 'zod';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';

export const ExecuteApiModelSchema = z.object({
  executionId: z.string(),
  variables: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string(),
      value: z.string().min(1).max(VARIABLE_MAX_LENGTH),
    }),
  ),
});

export type ExecuteApiModel = z.infer<typeof ExecuteApiModelSchema>;
