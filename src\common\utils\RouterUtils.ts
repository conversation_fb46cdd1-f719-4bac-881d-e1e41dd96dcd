import * as pathToRegexp from 'path-to-regexp';
import { matchPath, type NavigateFunction, type NavigateOptions, type To } from 'react-router-dom';
export const ROUTE_PATH = {
  MONITOR_ALERT: '/monitor-alerts',
  REPORT: '/reports',
  EMAIL: '/emails',
  ADMIN: '/admins',
  EVENT: '/events',
  EXPORT_DATA: '/export-datas',
  LOGIN: '/login',
  EXECUTION: '/executions',
  ALERT_REQUEST: '/alert-requests',
  ALERT_REQUEST_CREATE: 'create',
  SUPERIORS: '/admins/superiors/sql-execution',
  // Input data paths
  INPUT_DATA: '/admins/input-data',
  WEBHOOK: '/admins/input-data/webhooks',
  DATABASE_COLLECT: '/admins/input-data/database-collects',
  EMAIL_COLLECT: '/admins/input-data/email-collects',
  DATABASE_THRESHOLD: '/admins/input-data/database-thresholds',
  // Alert config path
  ALERT_CONFIG: '/admins/alert-config',
  SERVICE_APPLICATION: '/admins/alert-config/services-applications',
  PRIORITY_CONFIG: '/admins/alert-config/priority-configs',
  GROUP_CONFIG: '/admins/alert-config/group-configs',
  MAINTENANCE_TIME_CONFIG: '/admins/alert-config/maintenance-times-configs',
  FILTER_ALERT_CONFIG: '/admins/alert-config/filter-alerts-configs',
  MODIFY_ALERT_CONFIG: '/admins/alert-config/modify-alerts-configs',
  // Email config path
  EMAIL_CONFIG: '/admins/email-config',
  PARTNER_MANAGEMENT: '/admins/email-config/partner-managements',
  EMAIL_CONNECTION: '/admins/email-config/email-connections',
  EMAIL_TEMPLATE: '/admins/email-config/email-templates',
  // Database config path
  DATABASE_CONFIG: '/admins/database-config',
  DATABASE_CONNECTION: '/admins/database-config/database-connections',
  QUERY_SQL_CONFIG: '/admins/database-config/query-sqls',

  // Other admin path
  PERMISSION_MANAGERMENT: '/admins/permsision-managements',
  CUSTOM_OBJECT: '/admins/custom-objects',
  TELEGRAM_ALERT_CONFIG: '/admins/telegram-managements',
  TEAMS_ALERT_CONFIG: '/admins/teams-managements',

  // Syslog
  SYS_LOG: '/admins/logs',

  // Execution
  EXECUTION_CONFIG_ROOT: '/admins/executions',
  EXECUTION_CONFIG: '/admins/executions/execution-configs',
  EXECUTION_HISTORY: '/admins/executions/execution-histories',
  VARIABLE: '/admins/variables',

  //Auto Trigger Action Config
  AUTO_TRIGGER_ACTION_CONFIGS: '/admins/auto-trigger-action-configs',

  // RPA Monitor Web path
  RPA_MONITOR_WEB: '/admins/rpa-monitor-web',
  RPA_CONFIG: '/admins/rpa-monitor-web/rpa-config',
  RPA_TEST: '/admins/rpa-monitor-web/rpa-test',
  WEB_MONITOR_CONFIG: '/admins/rpa-monitor-web/web-monitor-config',

  //Notification Event
  NOTIFICATION_EVENT: '/admins/notification-events',
};

type HistoryRouterType = {
  navigate?: NavigateFunction;
};
export const HistoryRouter: HistoryRouterType = {
  navigate: undefined,
};

// export const browserHistory = createBrowserHistory();

export const navigateTo = (to: To, options?: NavigateOptions | undefined) => {
  if (HistoryRouter.navigate) {
    HistoryRouter.navigate(to, options);
  }
};

/**
 *
 * @param aliasPath /ci-types/:id
 * @param realPath localhost/ci-types/4334
 * @returns true
 */
export const isMatchPath = (aliasPath: string, realPath: string) => {
  return !!matchPath(realPath, aliasPath);
};

/**
 *
 * @param aliasPath /ci-types/:id
 * @param realPath /localhost/ci-types/4334
 * @returns `{id: 4334}`
 */
export const parsePathToObject = (aliasPath: string, realPath: string, options?: Parameters<typeof pathToRegexp.pathToRegexp>[2]) => {
  const keys: pathToRegexp.Key[] = [];
  const regexp = pathToRegexp.pathToRegexp(aliasPath, keys, options);

  const match = regexp.exec(realPath);

  const params: Record<string, any> = {};

  if (match) {
    keys.forEach((key, index) => {
      params[key.name] = match[index + 1];
    });
  }
  return params;
};
