import React, { useEffect, useState } from 'react';
import { KanbanButton, KanbanSelect, KanbanInput, KanbanNumberInput, KanbanText } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { useForm, zodResolver } from '@mantine/form';
import useMutate from '@core/hooks/useMutate';
import { EmailConfig } from '@core/schema/EmailConfig';
import { EmailConfigApi } from '@api/EmailConfigApi';
import { getMaxLengthMessage } from '@common/utils/MessageUtils';
import { EmailProtocolTypeEnum } from '@common/constants/EmailProtocolTypeConstant';
import { EmailConfigModel, EmailConfigModelSchema } from '@models/EmailConfigModel';
import { EmailProtocolSecurityTypeEnum } from '@common/constants/EmailProtocolSecurityTypeConstant';
import { Email<PERSON><PERSON> } from '@api/EmailApi';
import Modal from '@components/Modal';
import WarningAlertComponent from '@components/WarningAlertComponent';
import { MAX_EMAIL_CONFIG_DOMAIN_LENGTH } from '@common/constants/ValidationConstant';
import { Flex } from '@mantine/core';

type CreateOrUpdateModalProps = {
  opened: boolean;
  onClose: () => void;
  refetchList: () => void;
  emailConfig?: EmailConfig;
  isViewMode?: boolean;
};
const CHARACTER_NAME_EMAIL_CONFIG_MAX_LENGTH: number = 253;
const CHARACTER_DESCRIPTION_EMAIL_CONFIG_MAX_LENGTH: number = 300;
const DEFAULT_FORM_EMAIL_CONFIG: EmailConfigModel = {
  host: '',
  port: 0,
  password: '',
  description: '',
  username: '',
  protocolType: EmailProtocolTypeEnum.SMTP,
  securityType: EmailProtocolSecurityTypeEnum.NONE,
  active: true,
};

const CreateOrUpdateModal: React.FC<CreateOrUpdateModalProps> = ({ emailConfig, isViewMode, onClose, opened, refetchList }) => {
  const isUpdateMode = !!emailConfig;

  const { data: listCollectEmailConfig } = useFetch(EmailConfigApi.findAllByEmailConfigId(emailConfig?.id || 0), {
    enabled: isUpdateMode && opened && !isViewMode,
  });
  const [existCollectEmailConfig, setExistCollectEmailConfig] = useState(false);
  const { data: emailConfigDetail } = useFetch(EmailConfigApi.findById(emailConfig?.id || 0), {
    enabled: isUpdateMode && opened,
  });

  const { mutate: testConnection } = useMutate(EmailApi.testConnection, {
    successNotification: 'Test Connection Successfully',
  });
  const { mutate: testConnectAndSaveMutate } = useMutate(EmailApi.testConnection, {
    successNotification: {
      enable: false,
    },
    onSuccess: () => {
      saveMutate(EmailConfigModelSchema.parse(values));
    },
  });
  const { mutate: saveMutate } = useMutate(EmailConfigApi.save, {
    successNotification: isUpdateMode ? `Update Email Connection Successfully` : 'Create Email Connection Successfully',
    onSuccess: () => {
      setValues(DEFAULT_FORM_EMAIL_CONFIG);
      refetchList();
      onClose();
    },
  });
  const { getInputProps, isValid, setValues, validate, values } = useForm({
    initialValues: DEFAULT_FORM_EMAIL_CONFIG,
    validate: zodResolver(EmailConfigModelSchema),
    validateInputOnChange: true,
  });

  const handleSave = () => {
    if (!validate().hasErrors) {
      testConnectAndSaveMutate(EmailConfigModelSchema.parse(values));
    }
  };
  const handleTestConnection = () => {
    if (!validate().hasErrors) {
      testConnection(EmailConfigModelSchema.parse(values));
    }
  };

  useEffect(() => {
    if (!isUpdateMode) {
      setValues(DEFAULT_FORM_EMAIL_CONFIG);
      return;
    }
    if (emailConfigDetail?.data) {
      setValues({
        id: emailConfigDetail?.data?.id || 0,
        port: emailConfigDetail?.data?.port || 0,
        host: emailConfigDetail?.data?.host || '',
        password: '',
        username: emailConfigDetail?.data?.username || '',
        description: emailConfigDetail?.data?.description || '',
        securityType: emailConfigDetail?.data.securityType || EmailProtocolSecurityTypeEnum.NONE,
        protocolType: emailConfigDetail?.data.protocolType || EmailProtocolTypeEnum.IMAP,
        active: emailConfigDetail?.data.active || false,
      });
    }
  }, [emailConfigDetail?.data, opened, setValues, emailConfig, isUpdateMode]);
  useEffect(() => {
    if (listCollectEmailConfig?.data) {
      setExistCollectEmailConfig(listCollectEmailConfig?.data?.length > 0 || false);
    }
    if (!isUpdateMode) {
      setExistCollectEmailConfig(false);
    }
  }, [isUpdateMode, listCollectEmailConfig]);
  return (
    <Modal
      size={'xl'}
      opened={opened}
      onClose={() => {
        onClose();
        setValues(DEFAULT_FORM_EMAIL_CONFIG);
      }}
      title={
        emailConfig
          ? isViewMode
            ? `View Email Connection ${emailConfig.email}`
            : `Update Email Connection ${emailConfig.email}`
          : 'Create Email Connection'
      }
      actions={
        !isViewMode && (
          <KanbanButton onClick={handleSave} disabled={!isValid()}>
            Save
          </KanbanButton>
        )
      }>
      {listCollectEmailConfig?.data && !isViewMode && (
        <WarningAlertComponent
          mainEntity='Email connection'
          dependencyEntity='collect email configs'
          dependencies={listCollectEmailConfig?.data.map((e) => e.name)}
        />
      )}
      <form>
        <KanbanInput
          disabled={isViewMode}
          required
          label='Host'
          description={getMaxLengthMessage(CHARACTER_NAME_EMAIL_CONFIG_MAX_LENGTH)}
          {...getInputProps('host')}
          maxLength={CHARACTER_NAME_EMAIL_CONFIG_MAX_LENGTH}
        />
        <KanbanNumberInput
          disabled={isViewMode}
          required
          label='Port'
          {...getInputProps('port')}
          allowDecimal={false}
          allowNegative={false}
          onChange={(val) => {
            const numberValue = val === '' ? undefined : Number(val);
            getInputProps('port').onChange(numberValue);
          }}
        />
        <Flex direction='row' align='center'>
          <KanbanInput
            required
            label='Email'
            description={getMaxLengthMessage(MAX_EMAIL_CONFIG_DOMAIN_LENGTH)}
            {...getInputProps('username')}
            maxLength={MAX_EMAIL_CONFIG_DOMAIN_LENGTH}
            disabled={isViewMode || existCollectEmailConfig}
            style={{ flex: 4 }}
          />
          <KanbanText mt='xl' style={{ flex: 1, textAlign: 'left' }}>
            @mbbank.com.vn
          </KanbanText>
        </Flex>
        <KanbanInput
          disabled={isViewMode}
          required
          label='Password'
          type='password'
          description={getMaxLengthMessage(CHARACTER_DESCRIPTION_EMAIL_CONFIG_MAX_LENGTH)}
          {...getInputProps('password')}
          maxLength={CHARACTER_DESCRIPTION_EMAIL_CONFIG_MAX_LENGTH}
        />
        <KanbanSelect
          required
          label='Protocol type'
          {...getInputProps('protocolType')}
          data={Object.keys(EmailProtocolTypeEnum)}
          disabled={isViewMode || existCollectEmailConfig}
        />
        <KanbanSelect
          disabled={isViewMode}
          required
          label='Security type'
          {...getInputProps('securityType')}
          data={Object.keys(EmailProtocolSecurityTypeEnum)}
        />
        {!isViewMode && (
          <KanbanButton onClick={handleTestConnection} variant='light' disabled={!isValid()}>
            Test Connection
          </KanbanButton>
        )}
        <KanbanInput
          disabled={isViewMode}
          label='Description'
          description={getMaxLengthMessage(CHARACTER_DESCRIPTION_EMAIL_CONFIG_MAX_LENGTH)}
          {...getInputProps('description')}
          maxLength={CHARACTER_DESCRIPTION_EMAIL_CONFIG_MAX_LENGTH}
        />
      </form>
    </Modal>
  );
};

export default CreateOrUpdateModal;
