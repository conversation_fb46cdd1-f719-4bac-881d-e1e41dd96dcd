import React, { forwardRef, useEffect, useImperative<PERSON><PERSON>le, useState, useMemo } from 'react';
import { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import styles from './VariableMentionList.module.scss';

export interface VariableMentionListProps extends SuggestionProps {
  // Remove items prop - we'll fetch directly
}

export interface VariableMentionListActions {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
}

export const VariableMentionList = forwardRef<VariableMentionListActions, VariableMentionListProps>(({ command, query }, ref) => {
  // Fetch variables directly from API
  const { data: variableData } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Filter variables based on query using useMemo
  const allItems = useMemo(() => {
    const variables = variableData?.data || [];
    const lowerQuery = query.toLowerCase();

    return variables
      .filter((variable: any) => variable.name.toLowerCase().includes(lowerQuery))
      .map((variable: any) => ({
        id: variable.name,
        label: variable.name,
      }));
  }, [variableData, query]);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = allItems[index];

    if (item) {
      command({ id: item.id, label: item.label });
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + allItems.length - 1) % allItems.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % allItems.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [allItems]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  // If no items, return null
  if (allItems.length === 0) {
    return (
      <div className={styles.mentionList}>
        <div className={styles.noResults}>No variables found</div>
      </div>
    );
  }

  return (
    <div className={styles.mentionList}>
      {allItems.map((item, index) => (
        <button
          className={`${styles.mentionItem} ${index === selectedIndex ? styles.selected : ''}`}
          key={index}
          onClick={() => selectItem(index)}
          type='button'>
          <div className={styles.variableIcon}>@@</div>
          <div className={styles.variableInfo}>
            <div className={styles.variableName}>{item.label}</div>
            <div className={styles.variableFormat}>{`{{${item.id}}}`}</div>
          </div>
        </button>
      ))}
    </div>
  );
});

VariableMentionList.displayName = 'VariableMentionList';
