import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { SuggestionProps } from '@tiptap/suggestion';
import styles from './VariableMentionList.module.scss';

export interface VariableMentionListProps extends SuggestionProps {
  items: { id: string; label: string }[];
}

export interface VariableMentionListActions {
  onKeyDown: (props: { event: KeyboardEvent }) => boolean;
}

export const VariableMentionList = forwardRef<VariableMentionListActions, VariableMentionListProps>((props, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const selectItem = (index: number) => {
    const item = props.items[index];

    if (item) {
      props.command({ id: item.id, label: item.label });
    }
  };

  const upHandler = () => {
    setSelectedIndex((selectedIndex + props.items.length - 1) % props.items.length);
  };

  const downHandler = () => {
    setSelectedIndex((selectedIndex + 1) % props.items.length);
  };

  const enterHandler = () => {
    selectItem(selectedIndex);
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter') {
        enterHandler();
        return true;
      }

      return false;
    },
  }));

  return (
    <div className={styles.mentionList}>
      {props.items.length ? (
        props.items.map((item, index) => (
          <button
            className={`${styles.mentionItem} ${index === selectedIndex ? styles.selected : ''}`}
            key={index}
            onClick={() => selectItem(index)}
            type='button'>
            <div className={styles.variableIcon}>@@</div>
            <div className={styles.variableInfo}>
              <div className={styles.variableName}>{item.label}</div>
              <div className={styles.variableFormat}>{`{{${item.id}}}`}</div>
            </div>
          </button>
        ))
      ) : (
        <div className={styles.noResults}>No variables found</div>
      )}
    </div>
  );
});

VariableMentionList.displayName = 'VariableMentionList';
