#!/usr/bin/env groovy
library('cicd-shared-lib-dev@tanzu') _

import java.text.SimpleDateFormat

// node("${NODE_INTEGRATION_TEAM_MGT}"){
node("${NODE_INTEGRATION_TEAM_MGT}") {    
    // =================================================================================
    /**
     * TODO: Chi thay doi 2 thong tin tren tuong ung voi project
     *
     * project_name: Name of project
     * kubernetes_namespace: namespace of K8S
     */
    def project_name = "mbmonitor-frontend"
    def kubernetes_namespace = "uat-mbmonitor"
    
    // ===================================================================================
    def image_version = "1.0.1"
    def chart_version = "0.1.${BUILD_NUMBER}"
    def environment = "L01"
    def harbor_host = vault path: 'cicd/harbor', key: 'harbor_tanzu_dev', credentialsId: 'vault-token-dev', engineVersion: "2"
    def helm_template = vault path: 'cicd/helm', key: 'helm_template_common', credentialsId: 'vault-token-dev', engineVersion: "2"
    def helm_host_argocd = "argocd-helm-tanzu-dev-L01"
    def docker_prefix = "${harbor_host}/${kubernetes_namespace}"
    def NODE_HOME="/sharedata/node-v16.14.0-linux-x64/bin"

    // ===================================================================================
    
    /**
     * TODO: Chon luong deploy
     * Deploy theo luong argocd : CICD-ARGOCD
     * Deploy theo luong helm : CICD-HELM
        môi trường intenal : sub_path = "dev"
        môi trường aws: sub_path = "aws-dev"
        môi trường azure : sub_path = "azure-dev"
        môi trường tanzu : sub_path = "tanzu-dev"
     */
    def Options = "CICD-HELM"
    def sub_path = "tanzu-uat"

    // ===================================================================================
    
    ci_common(project_name, docker_prefix, image_version, harbor_host, helm_template, Options, sub_path)
    try {
        def arrProject = environment.split(' ')
        def stages = [failFast: true]
        if(arrProject.length > 0 ){
            for (int i = 0; i < arrProject.length; i++) {
                def env = arrProject[i];
                switch(env) {
                    case "L01":
                        helm_host = "${helm_host_argocd}"
                        /*
                        * TODO : Truy thong tin cum
                        **** uat <=> uat-tanzu01
                        */
                        env_argocd = "uat"
                        break
                    }
                stage("Start ${arrProject[i]} Environment"){
                    script {
                        helm_pack_common(project_name, env, helm_host, helm_template, docker_prefix, image_version, Options, chart_version, sub_path) 
                        cd_common(project_name, helm_host, arrProject, stages, docker_prefix, image_version, env, Options, kubernetes_namespace, env_argocd, chart_version) 
                    }
                }
            }
        }
    } catch (Exception ex) {
        currentBuild.result = "Failed"
        throw ex
    }
}

def ci_common(def project_name, def docker_prefix, def image_version, def harbor_host, def helm_template, def Options, def sub_path) {
    def NODE_HOME="/sharedata/node-v16.14.0-linux-x64/bin"

    stage('Git Clone') {
        script { ci_common_java () }
    }

    stage('Prepare'){
        env.NODE_HOME="${NODE_HOME}"
        env.PATH="${NODE_HOME}:$PATH"
    }

    stage('NPM build') {
        sh "rm -rf node_modules"
        sh "rm -rf .npmrc"

        sh "npm install -g npm@8.5.5 --force"
        sh "npm install -g env-cmd"
        sh "npm install --save-dev @rspack/binding-linux-x64-gnu"
        sh "npm install --save-dev sass-embedded-linux-x64"

        sh "npm install --force"

        sh "npm run build-tanzu-uat"
    }

    stage('Docker Build') {
        script { docker_build_push(project_name, docker_prefix, image_version, harbor_host, sub_path) }
    }
}

