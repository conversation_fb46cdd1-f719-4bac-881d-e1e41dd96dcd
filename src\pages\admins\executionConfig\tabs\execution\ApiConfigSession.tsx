import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { UseFormReturn, useWatch, FormProvider } from 'react-hook-form';
import { ExecutionModel } from '@models/ExecutionModel';
import { VariableApi } from '@api/VariableApi';
import useFetch from '@core/hooks/useFetch';
import { ExecutionParam } from '@core/schema/ExecutionParam';
import { Variable } from '@core/schema/Variable';
import { EXECUTION_API_PARAM_VALUE_PREFIX } from '../../Constants';
import Section from './Section';
import ExecutionApiForm from '@components/executionApiForm/ExecutionApiForm';
import { DEFAULT_API_INFO } from './ExcecutionDefaultValues';

interface Props {
  form: UseFormReturn<ExecutionModel>;
  isViewMode: boolean;
  executionParams?: Array<ExecutionParam>;
}

const ApiConfigSession: React.FC<Props> = ({ executionParams, form, isViewMode }) => {
  const { control } = form;
  const editorMapRef = useRef<Record<string, { insert: (text: string, pos: number) => void }>>({});

  const registerEditor = useCallback((fieldName: string, insertFn: (text: string, pos: number) => void) => {
    editorMapRef.current[fieldName] = {
      insert: (text, pos) => {
        insertFn(text, pos);
      },
    };
  }, []);

  // Watch apiInfo to ensure it's always initialized
  const currentApiInfo = useWatch({ control, name: 'apiInfo' });

  useEffect(() => {
    // Always ensure apiInfo exists when this component is rendered
    if (!currentApiInfo) {
      const defaultApiInfo = { ...DEFAULT_API_INFO };

      form.setValue('apiInfo', defaultApiInfo, { shouldDirty: false });
    }
  }, [currentApiInfo, form]);

  // Load variables for popup
  const { data: varData } = useFetch(VariableApi.findAll(), { showLoading: false });

  // Get execution params from form for highlighting
  const formExecutionParams = useWatch({ control, name: 'apiInfo.executionParams' });

  // Combine variables from API, form executionParams, and execution variables for highlighting
  const variableNames = useMemo(() => {
    const apiVariables =
      varData?.data?.map((variable: Variable) => {
        const name = variable.name;
        return name.startsWith(EXECUTION_API_PARAM_VALUE_PREFIX) ? name : `${EXECUTION_API_PARAM_VALUE_PREFIX}${name}`;
      }) || [];

    const formParams = formExecutionParams || [];

    // Add variables from execution data (from API response when editing)
    const responseVariables =
      executionParams?.map((variable: ExecutionParam) => {
        const name = variable.name;
        return name.startsWith(EXECUTION_API_PARAM_VALUE_PREFIX) ? name : `${EXECUTION_API_PARAM_VALUE_PREFIX}${name}`;
      }) || [];

    // Merge all variables
    const allVariables = [...new Set([...apiVariables, ...formParams, ...responseVariables])];

    return allVariables;
  }, [varData, formExecutionParams, executionParams]);

  return (
    <Section label='API config'>
      <FormProvider {...form}>
        <ExecutionApiForm isViewMode={isViewMode} variableNames={variableNames} registerEditor={registerEditor} />
      </FormProvider>
    </Section>
  );
};

export default ApiConfigSession;
