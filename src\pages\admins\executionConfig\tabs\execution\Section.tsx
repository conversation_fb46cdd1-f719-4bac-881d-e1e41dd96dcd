import { Box, Collapse } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanTitle } from 'kanban-design-system';
import React from 'react';
import classes from '@components/executionApiForm/ApiConfigSession.module.css';
import { IconChevronDown } from '@tabler/icons-react';

interface Props {
  label: string;
  children: React.ReactNode;
}

const Section = ({ children, label }: Props) => {
  const [opended, { toggle }] = useDisclosure(true);
  return (
    <Box className={classes.collapsibleSection}>
      <Box className={classes.sectionHeader} onClick={() => toggle()} style={{ cursor: 'pointer' }}>
        <KanbanTitle className={classes.sectionTitle} fw={600} size='lg'>
          {label}
        </KanbanTitle>
        <IconChevronDown className={`${classes.chevronIcon} ${opended ? classes.expanded : ''}`} size={20} color='var(--mantine-color-gray-6)' />
      </Box>
      <Collapse in={opended}>
        <Box className={classes.sectionContent}>{children}</Box>
      </Collapse>
    </Box>
  );
};

export default Section;
