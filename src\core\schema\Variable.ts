import { DataTypeEnum, VariableTypeEnum } from '@common/constants/ExecutionConstants';
import { z } from 'zod';

export const VariableSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  value: z.string().optional(),
  hidden: z.boolean(),
  type: z.nativeEnum(VariableTypeEnum).optional(),
  executionId: z.string().optional(),
  executionName: z.string().optional(),
  dataType: z.nativeEnum(DataTypeEnum).optional(),
  jsonPath: z.string().optional(),
  expirationTime: z.number().optional(),
  enableExpiration: z.boolean().optional(),
});

export type Variable = z.infer<typeof VariableSchema>;
