import { AuthTypeEnum, BodyTypeEnum, ExecutionTypeEnum, HttpVersionEnum, MethodTypeEnum } from '@common/constants/ExecutionConstants';
import { ExecutionModel } from '@models/ExecutionModel';
import { ExecutionApiInfoModel } from '@models/ExecutionApiInfoModel';

export const DEFAULT_EXECUTION_BASE = {
  id: undefined,
  name: '',
  description: '',
  executionGroupId: '',
  databaseConnectionId: undefined,
  script: undefined,
} as const;

export const DEFAULT_KEY_VALUE = { key: '', value: '', description: '', enable: false };
export const DEFAULT_API_INFO: ExecutionApiInfoModel = {
  url: '',
  method: MethodTypeEnum.GET,
  httpVersion: HttpVersionEnum.HTTP1x,
  enableSsl: false,
  executionParams: [],
  authentication: { authType: AuthTypeEnum.NONE, token: '', username: '', password: '' },
  body: {
    bodyType: BodyTypeEnum.NONE,
    contentType: undefined,
    bodyRaw: '',
    formUrlEncoded: [DEFAULT_KEY_VALUE],
  },
  headers: [DEFAULT_KEY_VALUE],
  params: [DEFAULT_KEY_VALUE],
};

export const getDefaultExecutionValues = (type: ExecutionTypeEnum): ExecutionModel => ({
  ...DEFAULT_EXECUTION_BASE,
  type,
  // ...(type === ExecutionTypeEnum.API && { apiInfo: { ...DEFAULT_API_INFO } }),
});
