import { DATABASE_ORACLE_CONNECT_TYPE, DATABASE_TYPE } from '@common/constants/DatabaseConnectionConstants';
import { COMMON_VALID_NAME_REGEX } from '@common/constants/RegexConstant';
import {
  DATABASE_CONNECTION_NAME_MAX_LENGTH,
  DATABASE_DESCRIPTION_MAX_LENGTH,
  DATABASE_HOST_MAX_LENGTH,
  DATABASE_NAME_MAX_LENGTH,
  DATABASE_PASSWORD_MAX_LENGTH,
  DATABASE_PORT_MAX_LENGTH,
  DATABASE_PORT_MIN_LENGTH,
  DATABASE_SID_OR_SERVICE_MAX_LENGTH,
  DATABASE_USERNAME_MAX_LENGTH,
} from '@common/constants/ValidationConstant';
import { REGEX_INPUT_MESSAGE_ERROR, STRING_CANT_BE_EMPTY_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { z, ZodIssueCode } from 'zod';

export const DatabaseConnectionModelSchema = z
  .object({
    id: z.number().optional(),
    name: z.string().trim().min(1).max(DATABASE_NAME_MAX_LENGTH),
    description: z.string().max(DATABASE_DESCRIPTION_MAX_LENGTH).optional(),
    type: z.nativeEnum(DATABASE_TYPE),
    host: z.string().trim().min(1).max(DATABASE_HOST_MAX_LENGTH).regex(COMMON_VALID_NAME_REGEX, { message: REGEX_INPUT_MESSAGE_ERROR }),
    port: z.number().min(DATABASE_PORT_MIN_LENGTH).max(DATABASE_PORT_MAX_LENGTH),
    sid: z.string().max(DATABASE_SID_OR_SERVICE_MAX_LENGTH).optional(),
    serviceName: z.string().max(DATABASE_SID_OR_SERVICE_MAX_LENGTH).optional(),
    userName: z.string().trim().min(1).max(DATABASE_USERNAME_MAX_LENGTH).regex(COMMON_VALID_NAME_REGEX, { message: REGEX_INPUT_MESSAGE_ERROR }),
    password: z.string().max(DATABASE_PASSWORD_MAX_LENGTH).optional(),
    databaseName: z.string().max(DATABASE_CONNECTION_NAME_MAX_LENGTH).optional(),
    oracleConnectType: z.nativeEnum(DATABASE_ORACLE_CONNECT_TYPE).optional(),
  })
  .superRefine((data, ctx) => {
    if (DATABASE_TYPE.ORACLE === data.type) {
      if (data.oracleConnectType === DATABASE_ORACLE_CONNECT_TYPE.SID && (!data.sid || data.sid.trim().length === 0)) {
        ctx.addIssue({
          message: STRING_CANT_BE_EMPTY_MESSAGE_ERROR,
          path: ['sid'],
          code: ZodIssueCode.custom,
        });
      }

      if (data.oracleConnectType === DATABASE_ORACLE_CONNECT_TYPE.SERVICE_NAME && (!data.serviceName || data.serviceName.trim().length === 0)) {
        ctx.addIssue({
          message: STRING_CANT_BE_EMPTY_MESSAGE_ERROR,
          path: ['serviceName'],
          code: ZodIssueCode.custom,
        });
      }
    } else {
      if (!data.databaseName || data.databaseName?.trim().length === 0) {
        ctx.addIssue({
          message: STRING_CANT_BE_EMPTY_MESSAGE_ERROR,
          path: ['databaseName'],
          code: ZodIssueCode.custom,
        });
      }
    }

    if (!data.id && (!data.password || data.password.trim().length === 0)) {
      ctx.addIssue({
        message: STRING_CANT_BE_EMPTY_MESSAGE_ERROR,
        path: ['password'],
        code: ZodIssueCode.custom,
      });
    }
  });
export type DatabaseConnectionModel = z.infer<typeof DatabaseConnectionModelSchema>;
