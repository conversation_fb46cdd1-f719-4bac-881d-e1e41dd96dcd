import React, { useCallback, useEffect, useState } from 'react';
import { ActionIcon, Box, Code, Collapse, Flex, Loader, Stack, Title } from '@mantine/core';
import { Execution } from '@core/schema/Execution';
import { ExecutionTypeEnum } from '@common/constants/ExecutionConstants';
import PythonEditor from '@components/editor/PythonEditor';
import SQLEditor from '@components/editor/SQLEditor';
import { KanbanButton, KanbanInput } from 'kanban-design-system';
import { IconChevronDown, IconCode, IconDatabase, IconPlayerPlayFilled } from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { ExecuteScriptModel, ExecuteScriptModelSchema } from '@models/ExecuteScriptModel';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ExecuteScript } from '@core/schema/ExecuteScript';
import SQLResult from './SQLResult';
import useMutate from '@core/hooks/useMutate';
import { ExecutionApi } from '@api/ExecutionApi';
import { PaginationSqlCommandRequest } from '@models/QuerySqlExecuteModel';
import { VARIABLE_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { AxiosError } from 'axios';
import { EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE, EXECUTION_UPDATED_ERROR_CODE } from '../Constants';
import { refetchRequest } from '@common/utils/QueryUtils';
import { NotificationError, NotificationWarning } from '@common/utils/NotificationUtils';
import PythonResult from '@components/PythonResult';
import { defaultErrorNotification } from '@core/hooks/Utils';
import ApiExecutionEditor from './ExecutionApiEditor';

interface Props {
  execution: Execution;
}

const ExecutionResultSession = ({
  execution,
  form,
  handleExecutionUpdated,
  onExecute,
}: {
  execution: Execution;
  form: UseFormReturn<ExecuteScriptModel>;
  onExecute: () => void;
  handleExecutionUpdated: () => void;
}) => {
  useEffect(() => {
    setResult(undefined);
    // Use to reset result when execution change
  }, [execution]);
  const [result, setResult] = useState<ExecuteScript | undefined>(undefined);
  const { formState, getValues } = form;
  const [isExecuting, setIsExecuting] = useState(false);

  const { isPending, mutate: executeMutate } = useMutate(ExecutionApi.execute, {
    showLoading: false,
    errorNotification: { enable: false },
    onSuccess(data) {
      if (data.data) {
        setResult(data.data);
      }
    },

    onError(error) {
      if (error instanceof AxiosError) {
        if (error.code === EXECUTION_UPDATED_ERROR_CODE) {
          handleExecutionUpdated();
          NotificationWarning({ title: 'Reload execution information', message: 'Execution info has been changed!' });
          return;
        } else if (error.code === EXECUTION_IS_RUNNING_LONGER_THAN_EXPECTED_ERROR_CODE) {
          NotificationWarning({
            title: 'Execution is running longer than expected.',
            message: 'Can view the results upon completion on the execution history',
          });
          return;
        }
      }
      NotificationError(defaultErrorNotification(error));
    },
  });

  useEffect(() => {
    setIsExecuting(false);
  }, [execution]);

  useEffect(() => {
    setIsExecuting(isPending);
  }, [isPending]);

  const onExecuteClick = useCallback(() => {
    executeMutate(getValues());
    onExecute();
  }, [executeMutate, getValues, onExecute]);
  return (
    <>
      <Box>
        <KanbanButton fullWidth={false} disabled={!formState.isValid || isExecuting} onClick={onExecuteClick}>
          <Box mr='xs'>{isExecuting ? <Loader size='sm' /> : <IconPlayerPlayFilled size={16} />}</Box>
          {isExecuting ? 'Executing' : 'Execute'}
        </KanbanButton>
      </Box>
      {result && (
        <Box style={{ flex: 1, overflowY: 'auto' }}>
          {execution.type === ExecutionTypeEnum.PYTHON ? (
            <PythonResult error={result.scriptError} response={result.scriptResponse} />
          ) : (
            <Box style={{ borderRadius: 'var(--mantine-radius-xs)', overflow: 'hidden', flex: 1 }}>
              <Box bg='gray.7'>
                <SQLResult
                  executeScript={result}
                  onPaginationChange={(page, size) => {
                    const oldValue = getValues();
                    if (oldValue.paginationRequest?.page !== page || oldValue.paginationRequest?.size !== size) {
                      executeMutate({ ...oldValue, paginationRequest: { page, size } });
                    }
                  }}
                />
              </Box>
            </Box>
          )}
        </Box>
      )}
    </>
  );
};

const DEFAULT_PAGINATION_SQL_COMMAND_REQUEST: PaginationSqlCommandRequest = { page: 0, size: 20 };

const ScriptExecutionEditor = ({ execution }: Props) => {
  const { description, id, script, type, variables } = execution;
  const [opened, { close, open, toggle }] = useDisclosure(true);

  const form = useForm<ExecuteScriptModel>({
    defaultValues: {
      executionId: execution.id,
      paginationRequest: DEFAULT_PAGINATION_SQL_COMMAND_REQUEST,
      variables: [],
    },
    resolver: zodResolver(ExecuteScriptModelSchema),
  });
  const { control, register, reset } = form;
  useEffect(() => {
    reset();
    open();
    // reset state when execution change
  }, [execution, open, reset]);
  const { fields } = useFieldArray({
    control,
    name: 'variables',
    keyName: 'fieldId',
  });
  useEffect(() => {
    reset({
      executionId: id,
      paginationRequest: type === ExecutionTypeEnum.SQL ? DEFAULT_PAGINATION_SQL_COMMAND_REQUEST : undefined,
      variables: variables?.map((variable) => ({ id: variable.id, name: variable.name, value: variable.value, type: variable.type })) || [],
      ...execution,
    });
  }, [execution, execution.id, execution.type, execution.variables, id, reset, type, variables]);

  const handleExecutionUpdated = useCallback(() => {
    refetchRequest(ExecutionApi.findByIdWithVariable(execution.id));
    open();
  }, [execution.id, open]);

  return (
    <Stack gap='xs' p='xs' h='var(--kanban-appshell-maxheight-content)'>
      <Stack gap='xs' style={{ border: '1px solid var(--mantine-color-gray-3)', borderRadius: 'var(--mantine-radius-xs)' }} p='xs'>
        <Flex align='center' justify='space-between'>
          <Flex align='center' justify='flex-start' gap='md'>
            <Box w={24} h={24}>
              {ExecutionTypeEnum.SQL === execution.type ? (
                <IconDatabase size={24} color='var(--mantine-color-blue-4)' />
              ) : (
                <IconCode size={24} color='var(--mantine-color-green-5)' />
              )}
            </Box>
            <Title order={4} c='primary'>
              {execution.name}
              {description && <Code ml='sm'>{description}</Code>}
            </Title>
          </Flex>

          <ActionIcon onClick={toggle} variant='outline' size='md'>
            <IconChevronDown size={20} />
          </ActionIcon>
        </Flex>
        <Collapse in={opened}>
          {ExecutionTypeEnum.SQL === type ? (
            <SQLEditor value={script} readOnly={true} height='300px' />
          ) : (
            <PythonEditor value={script} readOnly={true} height='300px' />
          )}
          {fields &&
            fields.length > 0 &&
            fields.map((field, index) => (
              <KanbanInput
                key={field.fieldId}
                label={field.name}
                {...register(`variables.${index}.value`)}
                disabled={!!field.id}
                maxLength={VARIABLE_MAX_LENGTH}
              />
            ))}
        </Collapse>
      </Stack>

      <ExecutionResultSession execution={execution} form={form} onExecute={close} handleExecutionUpdated={handleExecutionUpdated} />
    </Stack>
  );
};

const ExecutionEditor = ({ execution }: Props) => {
  // For API executions, use the dedicated ApiExecutionViewer
  if (execution.type === ExecutionTypeEnum.API) {
    return <ApiExecutionEditor execution={execution} />;
  }

  // For SQL and Python executions, use the existing logic
  return <ScriptExecutionEditor execution={execution} />;
};

export default ExecutionEditor;
