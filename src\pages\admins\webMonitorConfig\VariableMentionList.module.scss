.mentionList {
  background: var(--mantine-color-body);
  border: 1px solid var(--mantine-color-default-border);
  border-radius: var(--mantine-radius-md);
  box-shadow: var(--mantine-shadow-md);
  max-height: 200px;
  overflow-y: auto;
  padding: var(--mantine-spacing-xs);
  min-width: 200px;
  z-index: 1000;
}

.mentionItem {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-xs);
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  border: none;
  background: none;
  cursor: pointer;
  border-radius: var(--mantine-radius-sm);
  width: 100%;
  text-align: left;
  transition: background-color 150ms ease;
  color: var(--mantine-color-text);

  &:hover,
  &.selected {
    background-color: var(--mantine-color-gray-0);
  }

  &.selected {
    background-color: var(--mantine-color-blue-light);
  }

  @mixin dark {
    &:hover,
    &.selected {
      background-color: var(--mantine-color-dark-5);
    }

    &.selected {
      background-color: var(--mantine-color-blue-dark);
    }
  }
}

.variableIcon {
  background-color: var(--mantine-color-blue-filled);
  color: var(--mantine-color-white);
  border-radius: var(--mantine-radius-xs);
  padding: 2px 4px;
  font-size: var(--mantine-font-size-xs);
  font-weight: 700;
  min-width: 20px;
  text-align: center;
}

.variableInfo {
  flex: 1;
  min-width: 0;
}

.variableName {
  font-size: var(--mantine-font-size-sm);
  font-weight: 500;
  color: var(--mantine-color-text);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.variableFormat {
  font-size: var(--mantine-font-size-xs);
  color: var(--mantine-color-dimmed);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noResults {
  padding: var(--mantine-spacing-sm);
  text-align: center;
  color: var(--mantine-color-dimmed);
  font-size: var(--mantine-font-size-sm);
  font-style: italic;
}
