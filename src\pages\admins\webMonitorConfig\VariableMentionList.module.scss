.mentionList {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  padding: 4px;
  min-width: 200px;
  z-index: 1000;
}

.mentionItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  text-align: left;
  transition: background-color 0.15s ease;

  &:hover,
  &.selected {
    background-color: #f8f9fa;
  }

  &.selected {
    background-color: #e3f2fd;
  }
}

.variableIcon {
  background-color: #2196f3;
  color: white;
  border-radius: 3px;
  padding: 2px 4px;
  font-size: 10px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.variableInfo {
  flex: 1;
  min-width: 0;
}

.variableName {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.variableFormat {
  font-size: 12px;
  color: #6c757d;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noResults {
  padding: 12px;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
  font-style: italic;
}
